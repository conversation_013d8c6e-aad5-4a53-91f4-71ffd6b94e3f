'use client';
import Script from 'next/script';
import { useEffect, useRef, useState } from 'react';

/**
 * Next.js 15 App Router에서는 메타데이터를 컴포넌트에서 직접 반환하지 않습니다.
 * 대신 layout.tsx에서 metadata 객체를 export하거나 generateMetadata 함수를 사용해야 합니다.
 *
 * 이 컴포넌트는 헤더 UI와 GNB 기능을 담당합니다.
 */
export default function MainHeader() {
  const [isNavOpened, setIsNavOpened] = useState(false);
  const btnNavRef = useRef<HTMLButtonElement>(null);

  // common.js의 GNB 기능을 React로 구현
  const handleNavToggle = () => {
    const newNavState = !isNavOpened;
    setIsNavOpened(newNavState);

    // document.documentElement에 nav-opened 클래스 토글 (common.js와 동일한 동작)
    if (newNavState) {
      document.documentElement.classList.add('nav-opened');
    } else {
      document.documentElement.classList.remove('nav-opened');
    }

    // 버튼에 is-active 클래스 토글 (common.js와 동일한 동작)
    if (btnNavRef.current) {
      if (newNavState) {
        btnNavRef.current.classList.add('is-active');
      } else {
        btnNavRef.current.classList.remove('is-active');
      }
    }
  };

  // 컴포넌트 언마운트 시 클래스 정리
  useEffect(() => {
    const calculateMetrics = () => {
      const contents = Array.from(document.querySelectorAll('[class^=main__content]'));
      const visual = document.querySelector('.visual');
      const mainContainer = document.querySelector('.main__container');

      if (!contents.length || !mainContainer) return;

      const vh = window.innerHeight;
      const heights = contents.map((c: any) => c.clientHeight);
      const stickPoints: number[] = [];
      let accum = vh;

      for (let i = 0; i < contents.length; i++) {
        stickPoints.push(accum);
        accum += heights[i];
      }

      let lastFixedIndex = -1;

      const onScroll = () => {
        const scrollY = window.scrollY;
        const maxFixIndex = contents.length - 2;
        let paddingTopTarget = vh;

        if (scrollY + vh >= stickPoints[0] + heights[0]) {
          paddingTopTarget += heights[0];
          if (scrollY + vh >= stickPoints[1] + heights[1]) {
            paddingTopTarget += heights[1];
          }
        }

        (mainContainer as HTMLElement).style.paddingTop = `${paddingTopTarget}px`;

        for (let i = 0; i <= maxFixIndex; i++) {
          if (scrollY + vh >= stickPoints[i] + heights[i]) {
            if (lastFixedIndex <= i) {
              contents[i].classList.add('content-fixed');
              lastFixedIndex = i;
            }
          } else if (scrollY + vh < stickPoints[i] + heights[i] && i <= lastFixedIndex) {
            contents[i].classList.remove('content-fixed');
            lastFixedIndex = i - 1;
          }
        }
      };

      window.addEventListener('scroll', onScroll);
      window.addEventListener('resize', () => {
        calculateMetrics();
        onScroll();
      });

      return () => {
        window.removeEventListener('scroll', onScroll);
        window.removeEventListener('resize', calculateMetrics);
      };
    };
    const cleanup = calculateMetrics();
    return cleanup;
  }, []);

  useEffect(() => {
    return () => {
      // 컴포넌트가 언마운트될 때 nav-opened 클래스 제거
      document.documentElement.classList.remove('nav-opened');
    };
  }, []);
  return (
    <>
      {/* 외부 폰트 로드 */}
      <link href="https://cdn.jsdelivr.net/gh/sun-typeface/SUIT@2/fonts/static/woff2/SUIT.css" rel="stylesheet" />

      {/* CSS 파일들 */}
      <link rel="stylesheet" type="text/css" href="/assets/css/jquery.fullpage.min.css" />
      <link rel="stylesheet" type="text/css" href="/assets/css/swiper.jquery.min.css" />
      <link rel="stylesheet" type="text/css" href="/assets/css/common.css" />

      {/* Next.js Script 컴포넌트 사용 (성능 최적화) */}
      <Script src="/assets/js/jquery-3.6.0.min.js" strategy="beforeInteractive" />
      <Script src="/assets/js/swiper-bundle.min.js" strategy="beforeInteractive" />
      {/* common.js의 GNB 기능은 React로 구현했으므로 조건부 로딩 */}
      {/* <Script
        src="/assets/js/common.js"
        strategy="afterInteractive"
        onLoad={() => {
          // common.js가 로드된 후 GNB 이벤트 리스너 제거 (중복 방지)
          const existingBtnNav = document.querySelector('.btn__nav')
          if (existingBtnNav) {
            // 기존 이벤트 리스너를 제거하기 위해 클론 후 교체
            const newBtnNav = existingBtnNav.cloneNode(true)
            existingBtnNav.parentNode?.replaceChild(newBtnNav, existingBtnNav)
          }
        }}
      /> */}
      <Script src="/assets/js/scrolloverflow.js" strategy="afterInteractive" />
      <Script src="/assets/js/jquery.fullpage.min.js" strategy="afterInteractive" />
      <Script src="/assets/js/jquery.fullpage.extensions.min.js" strategy="afterInteractive" />
      {/* MainHeader UI */}
      <div className="header">
        <div className="inner">
          <h1>
            <a href="/">
              <span className="blind">gcube</span>
            </a>
          </h1>
          <div className="gnb">
            <button ref={btnNavRef} type="button" className="btn__nav" onClick={handleNavToggle}>
              <span className="blind">모바일 GNB 열기</span>
            </button>
            <div className="nav">
              <div className="nav-top">
                <div className="logo">
                  <a href="/">
                    <span className="blind">gcube</span>
                  </a>
                </div>
                <button
                  type="button"
                  className="btn__gpu"
                  onClick={() => {
                    // GPU 관리 페이지로 이동 또는 모달 열기
                    console.log('GPU 관리 클릭');
                  }}
                >
                  GPU 관리
                </button>
              </div>
              <ul>
                <li>
                  <a href="/price">Price</a>
                </li>
                <li>
                  <a href="/docs">Docs</a>
                </li>
                <li>
                  <a href="/faq">FAQ</a>
                </li>
                <li>
                  <a href="/contact">Contact</a>
                </li>
              </ul>
              <button
                type="button"
                className="btn__gpu"
                onClick={() => {
                  // GPU 관리 페이지로 이동 또는 모달 열기
                  console.log('GPU 관리 클릭');
                }}
              >
                GPU 관리
              </button>
            </div>
            <button
              type="button"
              className="btn__gpu"
              onClick={() => {
                // GPU 관리 페이지로 이동 또는 모달 열기
                console.log('GPU 관리 클릭');
              }}
            >
              GPU 관리
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
