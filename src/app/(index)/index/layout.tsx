import MainHeader from '@/components/main-header';
import { Metadata } from 'next';
import { headers } from 'next/headers';

export async function generateMetadata(): Promise<Metadata> {
  const headersList = await headers();

  const canonicalUrl = headersList.get('canonical') ?? '/';
  return {
    metadataBase: new URL('https://gcube.ai'),
    title: 'gcube | 지큐브 | 클라우드 GPU',
    description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.',
    keywords: ['GCUBE', 'gcube', '지큐브', 'NVIDIA GPU', 'AI'],

    // 뷰포트 설정
    viewport: {
      width: 'device-width',
      initialScale: 1.0,
      maximumScale: 1.0,
      minimumScale: 1.0,
      userScalable: false
    },

    // 테마 색상
    themeColor: '#ffffff',

    // Open Graph 메타데이터
    openGraph: {
      type: 'website',
      url: canonicalUrl,
      title: 'gcube | 지큐브 | 클라우드 GPU',
      description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.',
      siteName: '지큐브',
      locale: 'ko_KR',
      images: [
        {
          url: '/assets/images/img.png',
          width: 1920,
          height: 1080,
          alt: '지큐브 - 클라우드 GPU 서비스'
        }
      ]
    },

    // 트위터 카드
    twitter: {
      card: 'summary_large_image',
      title: 'gcube | 지큐브 | 클라우드 GPU',
      description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.',
      images: ['/assets/images/img.png']
    },

    // 정규 URL
    alternates: {
      canonical: canonicalUrl
    },

    // 아이콘 설정
    icons: {
      icon: '/assets/favi/favicon.ico',
      shortcut: '/assets/favi/favicon.ico'
    },

    // 기타 메타데이터
    other: {
      'http-equiv': 'X-UA-Compatible',
      content: 'IE=edge,chrome=1'
    }
  };
}

export default function IndexLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="wrap main--wrap">
      {/* 메인 사이트 헤더 */}
      <MainHeader />

      {/* 메인 컨텐츠 영역 */}
      <div className="main__container">{children}</div>
    </div>
  );
}
