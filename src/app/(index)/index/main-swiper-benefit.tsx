'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// Swiper 타입 정의
declare global {
  interface Window {
    Swiper: any;
  }
}

export default function MainSwiperBenefit() {
  const swiperRef = useRef<HTMLDivElement>(null);
  const [benefitSwiper, setBenefitSwiper] = useState<any>(null);
  const [activeTimer, setActiveTimer] = useState<NodeJS.Timeout | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  // 활성 애니메이션 추가 함수
  const addActiveAnimation = useCallback(() => {
    if (!swiperRef.current) return;

    const slides = swiperRef.current.querySelectorAll('.swiper-slide');
    if (slides.length === 0) return;

    slides.forEach((el) => el.classList.remove('active'));
    slides[0].classList.add('active');
    setActiveIndex(0);

    const timer = setInterval(() => {
      slides[activeIndex].classList.remove('active');
      const nextIndex = (activeIndex + 1) % slides.length;
      slides[nextIndex].classList.add('active');
      setActiveIndex(nextIndex);
    }, 4000);

    setActiveTimer(timer);
  }, [activeIndex]);

  // 활성 애니메이션 제거 함수
  const removeActiveAnimation = useCallback(() => {
    if (activeTimer) {
      clearInterval(activeTimer);
      setActiveTimer(null);
    }

    if (!swiperRef.current) return;
    const slides = swiperRef.current.querySelectorAll('.swiper-slide');
    slides.forEach((el) => el.classList.remove('active'));
    setActiveIndex(0);
  }, [activeTimer]);

  // Swiper 활성화 함수
  const enableSwiper = useCallback(() => {
    if (!benefitSwiper && window.Swiper && swiperRef.current) {
      const newSwiper = new window.Swiper(swiperRef.current, {
        pagination: {
          el: '.benefitSwiper .swiper-pagination',
          clickable: true
        },
        autoplay: {
          delay: 4000,
          disableOnInteraction: false
        }
      });
      setBenefitSwiper(newSwiper);
    }
  }, [benefitSwiper]);

  // Swiper 비활성화 함수
  const disableSwiper = useCallback(() => {
    if (benefitSwiper) {
      benefitSwiper.destroy(true, true);
      setBenefitSwiper(null);
    }
  }, [benefitSwiper]);

  // 반응형 Swiper 처리 함수
  const handleResponsiveSwiper = useCallback(() => {
    if (window.innerWidth <= 767) {
      enableSwiper();
    } else {
      disableSwiper();
      removeActiveAnimation();
      addActiveAnimation();
    }
  }, [enableSwiper, disableSwiper, removeActiveAnimation, addActiveAnimation]);

  // 컴포넌트 마운트 시 초기화
  useEffect(() => {
    // Swiper 라이브러리 로드 확인
    const checkSwiper = () => {
      if (window.Swiper) {
        handleResponsiveSwiper();
      } else {
        // Swiper가 로드되지 않았다면 잠시 후 다시 확인
        setTimeout(checkSwiper, 100);
      }
    };

    checkSwiper();

    // 리사이즈 이벤트 리스너 추가
    window.addEventListener('resize', handleResponsiveSwiper);

    // 컴포넌트 언마운트 시 정리
    return () => {
      removeActiveAnimation();
      disableSwiper();
      window.removeEventListener('resize', handleResponsiveSwiper);
    };
  }, [handleResponsiveSwiper, removeActiveAnimation, disableSwiper]);

  return (
    <>
      <div ref={swiperRef} className="swiper benefitSwiper">
        <ul className="swiper-wrapper benefit__list">
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>Global GPU Grid</strong>
              </dt>
              <dd>
                gcube는 Global GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU 공유경제
                서비스입니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                어떤 환경에도 적용가능한
                <br />
                <strong>맞춤형 공급</strong>
              </dt>
              <dd>
                클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스, 수요가 일정하지 않은 경우 등
                다양한 맞춤형 서비스를 제공합니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                다수의 국내 GPU 자원
                <br />
                확보로 <strong>높은 안정성</strong>
              </dt>
              <dd>
                국내 기반 서비스를 시작하며 다수의 공급자를 확보하여 다양한 GPU 자원, 빠른 네트워크를 통한 안정적인 서비스를
                제공합니다.{' '}
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>누구나 저렴하게</strong>
                <br />
                이용 할 수 있는 GPU
              </dt>
              <dd>공급자와의 직접 계약, 누구나 손쉽게 공급할 수 있는 기술로 공급 비용을 절감하여 보다 경제적으로 이용할 수 있습니다. </dd>
            </dl>
          </li>
        </ul>
        <div className="swiper-pagination"></div>
      </div>
    </>
  );
}
