'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// Swiper 타입 정의
declare global {
  interface Window {
    Swiper: any;
  }
}

export default function MainSwiperBenefit() {
  const swiperRef = useRef<HTMLDivElement>(null);
  const [benefitSwiper, setBenefitSwiper] = useState<any>(null);
  const activeTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isMobile, setIsMobile] = useState<boolean | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  // 활성 애니메이션 추가 함수
  const addActiveAnimation = useCallback(() => {
    // 기존 타이머가 있다면 먼저 정리
    if (activeTimerRef.current) {
      clearInterval(activeTimerRef.current);
      activeTimerRef.current = null;
    }

    if (!swiperRef.current) return;

    const slides = swiperRef.current.querySelectorAll('.swiper-slide');
    if (slides.length === 0) return;

    slides.forEach((el) => el.classList.remove('active'));
    slides[0].classList.add('active');

    let currentIndex = 0;
    const timer = setInterval(() => {
      slides[currentIndex].classList.remove('active');
      currentIndex = (currentIndex + 1) % slides.length;
      slides[currentIndex].classList.add('active');
      setActiveIndex(currentIndex);
    }, 4000);

    activeTimerRef.current = timer;
    setActiveIndex(0);
  }, []);

  // 활성 애니메이션 제거 함수
  const removeActiveAnimation = useCallback(() => {
    if (activeTimerRef.current) {
      clearInterval(activeTimerRef.current);
      activeTimerRef.current = null;
    }

    if (!swiperRef.current) return;
    const slides = swiperRef.current.querySelectorAll('.swiper-slide');
    slides.forEach((el) => el.classList.remove('active'));
    setActiveIndex(0);
  }, []);

  // Swiper 활성화 함수
  const enableSwiper = useCallback(() => {
    setBenefitSwiper((prevSwiper: any) => {
      if (!prevSwiper && window.Swiper && swiperRef.current) {
        return new window.Swiper(swiperRef.current, {
          pagination: {
            el: '.benefitSwiper .swiper-pagination',
            clickable: true
          },
          autoplay: {
            delay: 4000,
            disableOnInteraction: false
          }
        });
      }
      return prevSwiper;
    });
  }, []);

  // Swiper 비활성화 함수
  const disableSwiper = useCallback(() => {
    setBenefitSwiper((prevSwiper: any) => {
      if (prevSwiper) {
        prevSwiper.destroy(true, true);
        return null;
      }
      return prevSwiper;
    });
  }, []);

  // 반응형 Swiper 처리 함수 (debounced)
  const handleResponsiveSwiper = useCallback(() => {
    const currentIsMobile = window.innerWidth <= 767;

    // 상태가 변경되지 않았다면 아무것도 하지 않음
    if (isMobile === currentIsMobile) return;

    setIsMobile(currentIsMobile);

    if (currentIsMobile) {
      // 모바일로 전환: 애니메이션 제거하고 Swiper 활성화
      removeActiveAnimation();
      enableSwiper();
    } else {
      // 데스크톱으로 전환: Swiper 비활성화하고 애니메이션 활성화
      disableSwiper();
      addActiveAnimation();
    }
  }, [isMobile, enableSwiper, disableSwiper, removeActiveAnimation, addActiveAnimation]);

  // Debounced resize handler
  const debouncedHandleResize = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      handleResponsiveSwiper();
    }, 150); // 150ms debounce
  }, [handleResponsiveSwiper]);

  // 컴포넌트 마운트 시 초기화
  useEffect(() => {
    // 초기 상태 설정
    const initialIsMobile = window.innerWidth <= 767;
    setIsMobile(initialIsMobile);

    // Swiper 라이브러리 로드 확인 후 초기 설정
    const checkSwiper = () => {
      if (window.Swiper) {
        if (initialIsMobile) {
          enableSwiper();
        } else {
          addActiveAnimation();
        }
      } else {
        // Swiper가 로드되지 않았다면 잠시 후 다시 확인
        setTimeout(checkSwiper, 100);
      }
    };

    checkSwiper();

    // 리사이즈 이벤트 리스너 추가 (debounced)
    window.addEventListener('resize', debouncedHandleResize);

    // 컴포넌트 언마운트 시 정리
    return () => {
      // 모든 타이머 정리
      if (activeTimerRef.current) {
        clearInterval(activeTimerRef.current);
        activeTimerRef.current = null;
      }
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }

      // Swiper 정리
      setBenefitSwiper((prevSwiper: any) => {
        if (prevSwiper) {
          prevSwiper.destroy(true, true);
        }
        return null;
      });

      // 이벤트 리스너 제거
      window.removeEventListener('resize', debouncedHandleResize);
    };
  }, []); // 빈 의존성 배열로 마운트 시에만 실행

  return (
    <>
      <div ref={swiperRef} className="swiper benefitSwiper">
        <ul className="swiper-wrapper benefit__list">
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>Global GPU Grid</strong>
              </dt>
              <dd>
                gcube는 Global GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU 공유경제
                서비스입니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                어떤 환경에도 적용가능한
                <br />
                <strong>맞춤형 공급</strong>
              </dt>
              <dd>
                클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스, 수요가 일정하지 않은 경우 등
                다양한 맞춤형 서비스를 제공합니다.
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                다수의 국내 GPU 자원
                <br />
                확보로 <strong>높은 안정성</strong>
              </dt>
              <dd>
                국내 기반 서비스를 시작하며 다수의 공급자를 확보하여 다양한 GPU 자원, 빠른 네트워크를 통한 안정적인 서비스를
                제공합니다.{' '}
              </dd>
            </dl>
          </li>
          <li className="swiper-slide">
            <dl>
              <dt>
                <strong>누구나 저렴하게</strong>
                <br />
                이용 할 수 있는 GPU
              </dt>
              <dd>공급자와의 직접 계약, 누구나 손쉽게 공급할 수 있는 기술로 공급 비용을 절감하여 보다 경제적으로 이용할 수 있습니다. </dd>
            </dl>
          </li>
        </ul>
        <div className="swiper-pagination"></div>
      </div>
    </>
  );
}
