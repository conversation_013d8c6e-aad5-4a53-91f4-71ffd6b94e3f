export default function ChatPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">AI Chat</h1>
        <button className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">New Chat</button>
      </div>

      <div className="rounded-lg bg-white p-6 shadow-md">
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm text-white">AI</div>
            <div className="flex-1 rounded-lg bg-gray-100 p-3">
              <p>안녕하세요! 무엇을 도와드릴까요?</p>
            </div>
          </div>

          <div className="flex items-start justify-end space-x-3">
            <div className="max-w-xs flex-1 rounded-lg bg-blue-600 p-3 text-white">
              <p>Next.js에서 layout을 어떻게 사용하나요?</p>
            </div>
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm text-white">U</div>
          </div>
        </div>

        <div className="mt-6 flex items-center space-x-2">
          <input
            type="text"
            placeholder="메시지를 입력하세요..."
            className="flex-1 rounded-lg border border-gray-300 px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />
          <button className="rounded-lg bg-blue-600 px-6 py-2 text-white hover:bg-blue-700">전송</button>
        </div>
      </div>
    </div>
  );
}
