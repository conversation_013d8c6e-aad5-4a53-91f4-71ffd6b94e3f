export default function AgentLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="agent-layout">
      {/* Agent 전용 헤더 */}
      <header className="agent-header">
        <nav className="flex items-center justify-between bg-blue-600 p-4 text-white">
          <h1>AI Agent Dashboard</h1>
          <div className="flex space-x-4">
            <button>Settings</button>
            <button>Profile</button>
          </div>
        </nav>
      </header>

      {/* Agent 전용 사이드바 */}
      <div className="flex min-h-screen">
        <aside className="w-64 bg-gray-100 p-4">
          <nav className="space-y-2">
            <a href="/agent/chat" className="block rounded p-2 hover:bg-gray-200">
              Chat
            </a>
            <a href="/agent/history" className="block rounded p-2 hover:bg-gray-200">
              History
            </a>
            <a href="/agent/models" className="block rounded p-2 hover:bg-gray-200">
              Models
            </a>
          </nav>
        </aside>

        {/* 메인 컨텐츠 영역 */}
        <main className="flex-1 p-6">{children}</main>
      </div>

      {/* Agent 전용 푸터 */}
      <footer className="bg-gray-800 p-4 text-center text-white">
        <p>&copy; 2025 AI Agent Platform</p>
      </footer>
    </div>
  );
}
