{"version": "2.0.0", "tasks": [{"label": "Format with <PERSON><PERSON><PERSON>", "type": "shell", "command": "npm", "args": ["run", "format"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Check Prettier formatting", "type": "shell", "command": "npm", "args": ["run", "format:check"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}