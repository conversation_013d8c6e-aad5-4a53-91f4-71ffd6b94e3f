{
  "parser": "@typescript-eslint/parser", // Specifies the ESLint parser
  "extends": [
    "plugin:prettier/recommended",
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended", // Uses the recommended rules from @typescript-eslint,
    "next/core-web-vitals"
  ],
  "plugins": ["@typescript-eslint", "prettier"],
  "parserOptions": {
    "sourceType": "module"
  },
  "rules": {
    "prettier/prettier": ["error", { "endOfLine": "auto" }],

    "@typescript-eslint/ban-ts-comment": "off", // Allows the use of @ts-ignore
    "@typescript-eslint/no-explicit-any": "off", // Allows the use of 'any' type
    "import/no-anonymous-default-export": "off",
    // "jsx-a11y/alt-text": "off",
    "no-undef": "off",
    "@typescript-eslint/no-var-requires": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-this-alias": "off",
    // "import/no-anonymous-default-export": [
    //   "error",
    //   {
    //     "allowArray": false,
    //     "allowArrowFunction": false,
    //     "allowAnonymousClass": false,
    //     "allowAnonymousFunction": false,
    //     "allowCallExpression": true, // The true value here is for backward compatibility
    //     "allowNew": false,
    //     "allowLiteral": false,
    //     "allowObject": false
    //   }
    // ],
    "no-mixed-spaces-and-tabs": 0,
    "react/no-unescaped-entities": 0,
    "no-redeclare": 0,
    "@typescript-eslint/ban-types": 0,
    "prefer-const": 0,
    "@next/next/no-img-element": 0,
    "jsx-a11y/alt-text": [
      2,
      {
        "elements": ["img", "object", "area", "input[type=\"image\"]"],
        "img": ["Image"],
        "object": ["Object"],
        "area": ["Area"],
        "input[type=\"image\"]": ["InputImage"]
      }
    ]
  }
}
