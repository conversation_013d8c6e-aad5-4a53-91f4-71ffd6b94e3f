{"version": 3, "sources": ["jquery.fullpage.js"], "names": ["global", "factory", "define", "amd", "$", "document", "Math", "exports", "module", "require", "j<PERSON><PERSON><PERSON>", "window", "this", "undefined", "WRAPPER", "WRAPPER_SEL", "RESPONSIVE", "NO_TRANSITION", "DESTROYED", "ENABLED", "VIEWING_PREFIX", "ACTIVE", "ACTIVE_SEL", "COMPLETELY", "COMPLETELY_SEL", "SECTION", "SECTION_SEL", "SECTION_ACTIVE_SEL", "SECTION_FIRST_SEL", "SECTION_LAST_SEL", "TABLE_CELL", "TABLE_CELL_SEL", "AUTO_HEIGHT", "NORMAL_SCROLL", "SECTION_NAV", "SECTION_NAV_SEL", "SECTION_NAV_TOOLTIP", "SECTION_NAV_TOOLTIP_SEL", "SHOW_ACTIVE_TOOLTIP", "SLIDE", "SLIDE_SEL", "SLIDE_ACTIVE_SEL", "SLIDES_WRAPPER", "SLIDES_WRAPPER_SEL", "SLIDES_CONTAINER", "SLIDES_CONTAINER_SEL", "TABLE", "SLIDES_NAV", "SLIDES_NAV_SEL", "SLIDES_NAV_LINK_SEL", "SLIDES_ARROW", "SLIDES_ARROW_SEL", "SLIDES_PREV", "SLIDES_ARROW_PREV", "SLIDES_ARROW_PREV_SEL", "SLIDES_NEXT", "SLIDES_ARROW_NEXT", "SLIDES_ARROW_NEXT_SEL", "$window", "$document", "fn", "fullpage", "options", "hasClass", "displayWarnings", "$htmlBody", "$body", "FP", "extend", "menu", "anchors", "lockAnchors", "navigation", "navigationPosition", "navigationTooltips", "showActiveTooltip", "slidesNavigation", "slidesNavPosition", "scrollBar", "hybrid", "css3", "scrollingSpeed", "autoScrolling", "fitToSection", "fitToSectionDelay", "easing", "easingcss3", "loopBottom", "loopTop", "loopHorizontal", "continuousVertical", "continuousHorizontal", "scrollHorizontally", "interlockedSlides", "dragAndMove", "offsetSections", "resetSliders", "fadingEffect", "normalScrollElements", "scrollOverflow", "scrollOverflowReset", "scrollOverflowHandler", "fp_scrolloverflow", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollOverflowOptions", "touchSensitivity", "normalScrollElementTouchThreshold", "bigSectionsDestination", "keyboardScrolling", "animateAnchor", "recordHistory", "controlArrows", "controlArrowColor", "verticalCentered", "sectionsColor", "paddingTop", "paddingBottom", "fixedElements", "responsive", "responsiveWidth", "responsiveHeight", "responsiveSlides", "parallax", "parallaxOptions", "type", "percentage", "property", "sectionSelector", "slideSelector", "afterLoad", "onLeave", "afterRender", "afterResize", "afterReBuild", "afterSlideLoad", "onSlideLeave", "afterResponsive", "lazyLoading", "lastScrolledDestiny", "lastScrolledSlide", "controlPressed", "startingSection", "slideMoving", "isTouchDevice", "navigator", "userAgent", "match", "is<PERSON><PERSON>ch", "msMaxTouchPoints", "container", "windowsHeight", "height", "isResizing", "isWindowFocused", "canScroll", "scrollings", "isScrollAllowed", "m", "up", "down", "left", "right", "k", "scrollBarHandler", "resizeId", "afterSectionLoadsId", "afterSlideLoadsId", "scrollId", "scrollId2", "keydownId", "MSPointer", "pointer", "PointerEvent", "move", "getMS<PERSON>ointer", "events", "touchmove", "touchstart", "focusableElementsString", "originals", "easeInOutCubic", "x", "t", "b", "c", "d", "length", "version", "setAutoScrolling", "setRecordHistory", "setScrollingSpeed", "setFitToSection", "setLockAnchors", "value", "setMouseWheelScrolling", "setAllowScrolling", "setKeyboardScrolling", "moveSectionUp", "moveSectionDown", "silentMoveTo", "moveTo", "moveSlideRight", "moveSlideLeft", "reBuild", "setResponsive", "destroy", "all", "addClass", "clearTimeout", "off", "<PERSON><PERSON><PERSON><PERSON>", "hash<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resize<PERSON><PERSON>ler", "keydownHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silentScroll", "find", "each", "setSrc", "remove", "css", "background-color", "padding", "width", "position", "-ms-touch-action", "touch-action", "overflow", "removeClass", "get", "className", "split", "index", "indexOf", "attr", "data", "removeAnimation", "replaceWith", "childNodes", "-webkit-transition", "transition", "scrollTop", "usedSelectors", "destroyStructure", "shared", "afterRenderActions", "has3d", "el", "createElement", "transforms", "webkitTransform", "OTransform", "msTransform", "MozTransform", "transform", "body", "insertBefore", "style", "getComputedStyle", "getPropertyValue", "<PERSON><PERSON><PERSON><PERSON>", "support3d", "sections", "filter", "map", "toString", "section", "slides", "numSlides", "activateMenuAndNav", "closest", "appendTo", "slider<PERSON><PERSON><PERSON>", "slideWidth", "wrapAll", "parent", "wrap", "after", "hide", "append", "nav", "i", "first", "addSlidesNavigation", "addTableClass", "startingSlide", "silentLandscapeScroll", "eq", "styleSlides", "link", "li", "tooltip", "addVerticalNavigation", "element", "newParam", "originalSrc", "test", "init", "setBodyClass", "readyState", "scrollToAnchor", "on", "blur", "<PERSON><PERSON><PERSON><PERSON>", "resize", "keydown", "keyup", "sectionBulletHandler", "slideBulletHandler", "tooltipTextHandler", "slideArrowHandler", "isScrolling", "lastScroll", "touchStartY", "touchStartX", "touchEndY", "touchEndX", "prevTime", "Date", "getTime", "previousDestTop", "oldPageY", "previousHeight", "setVariableState", "top", "_addEventListener", "prefix", "addEventListener", "support", "onmousew<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addMouseWheelHandler", "mouseDownHandler", "mouseUpHandler", "removeEventListener", "detachEvent", "directions", "replace", "direction", "setIsScrollAllowed", "preventBouncing", "touchStartHandler", "touchMoveHandler", "prev", "last", "scrollPage", "next", "sectionAnchor", "slideAnchor", "destiny", "getSectionByAnchor", "scrollPageAndSlide", "moveSlide", "resizing", "slidesWrap", "getTableHeight", "landscapeScroll", "createScrollBarForAll", "sectionIndex", "isFunction", "call", "active", "isResponsive", "show", "destinationSection", "lazyLoad", "playMedia", "getAnchorsURL", "currentSection", "currentScroll", "scrollDirection", "visibleSectionIndex", "screen_mid", "isAtBottom", "querySelectorAll", "offsetTop", "movement", "bottom", "isCompletelyInViewPort", "siblings", "slideIndex", "slideAnchorLink", "leavingSection", "leavingSectionIndex", "yMovement", "getYmovement", "anchorLink", "activeSlide", "stopMedia", "setState", "setTimeout", "outerHeight", "scrolling", "scrollSection", "scrollable", "check", "isScrolled", "event", "e", "originalEvent", "is<PERSON><PERSON>ly<PERSON><PERSON><PERSON>", "preventDefault", "activeSection", "target", "touchEvents", "getEventsPage", "y", "abs", "outerWidth", "pointerType", "stop", "getAverage", "elements", "number", "sum", "lastElements", "slice", "max", "ceil", "curTime", "isNormalScroll", "wheelDelta", "deltaY", "detail", "delta", "min", "horizontalDetection", "wheelDeltaX", "deltaX", "isScrollingVertically", "shift", "push", "returnValue", "timeDiff", "currentSlide", "keepSlidesPosition", "callback", "isMovementUp", "elemPosition", "isScrollingDown", "sectionBottom", "v", "dtop", "is", "localIsResizing", "before", "nextAll", "prevAll", "reverse", "wrapAroundElements", "createInfiniteSections", "beforeLeave", "translate3d", "round", "transformContainer", "afterSectionLoads", "scrollSettings", "scroll", "getScrollSettings", "animate", "promise", "done", "performMovement", "attribute", "removeAttr", "getSlideOrSection", "typeToPlay", "load", "panel", "hasAttribute", "play", "playYoutube", "onload", "contentWindow", "postMessage", "pause", "slide", "isFirstSlideMove", "isFirstScrollMove", "hash", "location", "anchorsParts", "isFunkyAnchor", "decodeURIComponent", "activeElement", "keyCode", "which", "isShiftPressed", "shift<PERSON>ey", "focusableElements", "not", "preventAndFocusFirst", "focus", "onTab", "inArray", "ctrl<PERSON>ey", "shiftPressed", "onkeydown", "trigger", "pageY", "mouseMoveHandler", "destinyPos", "slidesNav", "getAnchor", "prevSlide", "prevSlideIndex", "xMovement", "fromIndex", "toIndex", "getXmovement", "toggle", "fireCallback", "addAnimation", "getTransforms", "afterSlideLoads", "scrollLeft", "performHorizontalMove", "currentHeight", "widthLimit", "heightLimit", "isBreakingPointWidth", "isBreakingPointHeight", "anchor", "name", "wrapper", "wrapInner", "sectionHeight", "paddings", "parseInt", "animated", "scrollSlider", "sectionHash", "setUrlHash", "url", "history", "replaceState", "baseUrl", "href", "text", "String", "classRe", "RegExp", "pageX", "touches", "noCallbacks", "roundedTop", "-webkit-transform", "-moz-transform", "-ms-transform", "Object", "keys", "key", "variable", "showError", "extension", "nameAttr", "toLowerCase", "idAttr", "console"], "mappings": ";;;;;;;CAOA,SAAUA,EAAQC,GACd,aACsB,mBAAXC,QAAyBA,OAAOC,IACvCD,QAAQ,UAAW,SAASE,GAC1B,OAAOH,EAAQG,EAAGJ,EAAQA,EAAOK,SAAUL,EAAOM,QAE1B,iBAAZC,SAAwBA,QACtCC,OAAOD,QAAUN,EAAQQ,QAAQ,UAAWT,EAAQA,EAAOK,SAAUL,EAAOM,MAE5EL,EAAQS,OAAQV,EAAQA,EAAOK,SAAUL,EAAOM,MATxD,CAWqB,oBAAXK,OAAyBA,OAASC,KAAM,SAASR,EAAGO,EAAQN,EAAUC,EAAMO,GAClF,aAGA,IAAIC,EAAwB,mBACxBC,EAAwB,IAAMD,EAO9BE,EAAwB,gBACxBC,EAAwB,kBACxBC,EAAwB,eACxBC,EAAwB,aACxBC,EAAwB,aACxBC,EAAwB,SACxBC,EAAwB,IAAMD,EAC9BE,EAAwB,gBACxBC,EAAwB,IAAMD,EAI9BE,EAAwB,aACxBC,EAAwB,IAAMD,EAC9BE,EAAwBD,EAAcJ,EACtCM,EAAwBF,EAAc,SACtCG,EAAwBH,EAAc,QACtCI,EAAwB,eACxBC,EAAwB,IAAMD,EAC9BE,EAAwB,iBAExBC,EAAwB,mBAIxBC,EAAwB,SACxBC,EAAwB,IAAMD,EAC9BE,EAAwB,aACxBC,EAAwB,IAAID,EAC5BE,EAAwB,iBAIxBC,EAAwB,WACxBC,EAAwB,IAAMD,EAC9BE,EAAwBD,EAAYlB,EACpCoB,EAAwB,YACxBC,EAAwB,IAAMD,EAC9BE,EAAwB,qBACxBC,EAAwB,IAAMD,EAC9BE,EAAwB,WAGxBC,EAAwB,eACxBC,EAAwB,IAAMD,EAC9BE,EAAwBD,EAAiB,KACzCE,EAAwB,kBACxBC,EAAwB,IAAMD,EAC9BE,EAAwB,UAExBC,EAAwBH,EAAe,IAAME,EAC7CE,EAAwBH,GAFA,IAAMC,GAG9BG,EAAwB,UAExBC,EAAwBN,EAAe,IAAMK,EAC7CE,EAAwBN,EAFA,WAIxBO,EAAUtD,EAAEO,GACZgD,EAAYvD,EAAEC,GAElBD,EAAEwD,GAAGC,SAAW,SAASC,GAErB,GAAG1D,EAAE,QAAQ2D,SAAS5C,GAAW6C,SAAjC,CAGA,IAAIC,EAAY7D,EAAE,cACd8D,EAAQ9D,EAAE,QAEV+D,EAAK/D,EAAEwD,GAAGC,SAGdC,EAAU1D,EAAEgE,QAERC,MAAM,EACNC,WACAC,aAAa,EACbC,YAAY,EACZC,mBAAoB,QACpBC,sBACAC,mBAAmB,EACnBC,kBAAkB,EAClBC,kBAAmB,SACnBC,WAAW,EACXC,QAAQ,EAGRC,MAAM,EACNC,eAAgB,IAChBC,eAAe,EACfC,cAAc,EACdC,kBAAmB,IACnBC,OAAQ,iBACRC,WAAY,OACZC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,qBAAsB,KACtBC,gBAAgB,EAChBC,qBAAqB,EACrBC,sBAAuBjG,EAAEwD,GAAG0C,kBAAoBlG,EAAEwD,GAAG0C,kBAAkBC,eAAiB,KACxFC,sBAAuB,KACvBC,iBAAkB,EAClBC,kCAAmC,EACnCC,uBAAwB,KAGxBC,mBAAmB,EACnBC,eAAe,EACfC,eAAe,EAGfC,eAAe,EACfC,kBAAmB,OACnBC,kBAAkB,EAClBC,iBACAC,WAAY,EACZC,cAAe,EACfC,cAAe,KACfC,WAAY,EACZC,gBAAiB,EACjBC,iBAAkB,EAClBC,kBAAkB,EAClBC,UAAU,EACVC,iBACIC,KAAM,SACNC,WAAY,GACZC,SAAU,aAIdC,gBA/HoB,WAgIpBC,cA3GoB,SA8GpBC,UAAW,KACXC,QAAS,KACTC,YAAa,KACbC,YAAa,KACbC,aAAc,KACdC,eAAgB,KAChBC,aAAc,KACdC,gBAAiB,KAEjBC,aAAa,GACd3E,GAGH,IAQI4E,EACAC,GAGAC,GACAC,GAbAC,IAAc,EAEdC,GAAgBC,UAAUC,UAAUC,MAAM,+GAC1CC,GAAY,iBAAkBxI,GAAYqI,UAAUI,iBAAmB,GAAOJ,UAAwB,eACtGK,GAAYjJ,EAAEQ,MACd0I,GAAgB5F,EAAQ6F,SACxBC,IAAa,EACbC,IAAkB,EAGlBC,IAAY,EACZC,MAGAC,IACJC,GAAuBC,IAAK,EAAMC,MAAO,EAAMC,MAAO,EAAMC,OAAQ,IACpEL,GAAgBM,EAAI9J,EAAEgE,QAAO,KAASwF,GAAgBC,GACtD,IAKIM,GAMAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAhBAC,GAq2EJ,WACI,IAAIC,EAIAA,EADDhK,EAAOiK,cACMb,KAAM,cAAec,KAAM,gBAK3Bd,KAAM,gBAAiBc,KAAM,iBAG7C,OAAOF,EAl3EKG,GACZC,IACAC,UAAW,gBAAiBrK,EAAS,YAAe+J,GAAUG,KAC9DI,WAAY,iBAAkBtK,EAAS,aAAgB+J,GAAUX,MAKjEmB,GAA0B,iLAS1BC,GAAY/K,EAAEgE,QAAO,KAAUN,GAEnCE,KAGA5D,EAAEgE,OAAOhE,EAAEiF,QAAS+F,eAAgB,SAAUC,EAAGC,EAAGC,EAAGC,EAAGC,GAAI,OAAKH,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAEF,EAAEA,EAAEA,EAAIC,EAASC,EAAE,IAAIF,GAAG,GAAGA,EAAEA,EAAI,GAAKC,KA2RhInL,EAAEQ,MAAM8K,SAEPvH,EAAGwH,QAAU,QACbxH,EAAGyH,iBAAmBA,GACtBzH,EAAG0H,iBAAmBA,GACtB1H,EAAG2H,kBAAoBA,GACvB3H,EAAG4H,gBAAkBA,GACrB5H,EAAG6H,eAlNP,SAAwBC,GACpBnI,EAAQS,YAAc0H,GAkNtB9H,EAAG+H,uBAAyBA,GAC5B/H,EAAGgI,kBAAoBA,GACvBhI,EAAGiI,qBAAuBA,GAC1BjI,EAAGkI,cAAgBA,GACnBlI,EAAGmI,gBAAkBA,GACrBnI,EAAGoI,aAAeA,GAClBpI,EAAGqI,OAASA,GACZrI,EAAGsI,eAAiBA,GACpBtI,EAAGuI,cAAgBA,GACnBvI,EAAGgB,aAAeA,GAClBhB,EAAGwI,QAAUA,GACbxI,EAAGyI,cAAgBA,GACnBzI,EAAG0I,QA6oEP,SAAiBC,GACblB,IAAiB,EAAO,YACxBO,IAAkB,GAClBC,IAAqB,GACrB/C,GAAU0D,SAAS7L,GAEnB8L,aAAa1C,IACb0C,aAAa3C,IACb2C,aAAa5C,IACb4C,aAAazC,IACbyC,aAAaxC,IAEb9G,EACKuJ,IAAI,SAAUC,IACdD,IAAI,aAAcE,IAClBF,IAAI,SAAUG,IAEnBzJ,EACKsJ,IAAI,UAAWI,IACfJ,IAAI,QAASK,IACbL,IAAI,mBAAoB9K,EAAkB,MAC1C8K,IAAI,aAAc9K,EAAkB,OACpC8K,IAAI,aAAc9K,EAAkB,OACpC8K,IAAI,mBAAoBhK,GACxBgK,IAAI,YAAanJ,EAAQoC,sBACzB+G,IAAI,WAAYnJ,EAAQoC,sBAE7B9F,EAAEsB,GACGuL,IAAI,mBAAoB9J,GAE7B6J,aAAa1C,IACb0C,aAAa3C,IAGVyC,GAQP,WAEIS,GAAa,GAGblE,GAAUmE,KAAK,sEAAsEC,KAAK,WACtFC,GAAOtN,EAAEQ,MAAO,SAGpByI,GAAUmE,KAAK,oBAAoBC,KAAK,WACpCC,GAAOtN,EAAEQ,MAAO,YAGpBR,EAAE+B,EAAkB,KAAOa,EAAkB,KAAOG,GAAkBwK,SAGtEvN,EAAEsB,GAAakM,KACXrE,OAAU,GACVsE,mBAAqB,GACrBC,QAAW,KAGf1N,EAAEoC,GAAWoL,KACTG,MAAS,KAGb1E,GAAUuE,KACNrE,OAAU,GACVyE,SAAY,GACZC,mBAAoB,GACpBC,eAAgB,KAGpBjK,EAAU2J,KACNO,SAAY,GACZ5E,OAAU,KAIdnJ,EAAE,QAAQgO,YAAYjN,GAGtB+C,EAAMkK,YAAYpN,GAGlBZ,EAAEqN,KAAKvJ,EAAMmK,IAAI,GAAGC,UAAUC,MAAM,OAAQ,SAAUC,EAAOF,GACf,IAAtCA,EAAUG,QAAQrN,IAClB8C,EAAMkK,YAAYE,KAK1BlO,EAAEsB,EAAc,KAAOc,GAAWiL,KAAK,WAChC3J,EAAQuC,uBACPvC,EAAQuC,sBAAsBsH,OAAOvN,EAAEQ,OAE3CR,EAAEQ,MAAMwN,YAAYtL,EAAQ,IAAMzB,GAClCjB,EAAEQ,MAAM8N,KAAK,QAAStO,EAAEQ,MAAM+N,KAAK,gBAGvCC,GAAgBvF,IAGhBA,GAAUmE,KAAKzL,EAAiB,KAAOc,EAAuB,KAAOF,GAAoB8K,KAAK,WAE1FrN,EAAEQ,MAAMiO,YAAYjO,KAAKkO,cAI7BzF,GAAUuE,KACNmB,qBAAsB,OACtBC,WAAc,SAIlB/K,EAAUgL,UAAU,GAGpB,IAAIC,GAAiBzN,EAASc,EAAOK,GACrCxC,EAAEqN,KAAKyB,EAAe,SAASV,EAAOvC,GAClC7L,EAAE,IAAM6L,GAAOmC,YAAYnC,KAvF3BkD,IA5qEJhL,EAAGiL,QACCC,mBAAoBA,IAQ5B,WAEOvL,EAAQkB,OACPlB,EAAQkB,KAo5DhB,WACI,IACIsK,EADAC,EAAKlP,EAASmP,cAAc,KAE5BC,GACIC,gBAAkB,oBAClBC,WAAa,eACbC,YAAc,gBACdC,aAAe,iBACfC,UAAY,aAMpB,IAAK,IAAIxE,KAFTjL,EAAS0P,KAAKC,aAAaT,EAAI,MAEjBE,EACNF,EAAGU,MAAM3E,KAAOzK,IAChB0O,EAAGU,MAAM3E,GAAK,2BACdgE,EAAQ3O,EAAOuP,iBAAiBX,GAAIY,iBAAiBV,EAAWnE,KAMxE,OAFAjL,EAAS0P,KAAKK,YAAYb,GAElBD,IAAUzO,GAAayO,EAAM5D,OAAS,GAAe,SAAV4D,EA36DhCe,IAGnBvM,EAAQgB,UAAYhB,EAAQgB,WAAahB,EAAQiB,OAqE7CuL,EAAWjH,GAAUmE,KAAK1J,EAAQiE,iBAGlCjE,EAAQQ,QAAQoH,SAChB5H,EAAQQ,QAAUgM,EAASC,OAAO,iBAAiBC,IAAI,WACnD,OAAOpQ,EAAEQ,MAAM+N,KAAK,UAAU8B,aAC/BpC,OAIHvK,EAAQY,mBAAmBgH,SAC3B5H,EAAQY,mBAAqB4L,EAASC,OAAO,kBAAkBC,IAAI,WAC/D,OAAOpQ,EAAEQ,MAAM+N,KAAK,WAAW8B,aAChCpC,OAQPhF,GAAUuE,KACNrE,OAAU,OACVyE,SAAY,aAIhB3E,GAAU0D,SAASjM,GACnBV,EAAE,QAAQ2M,SAAS5L,GAGnBmI,GAAgB5F,EAAQ6F,SAExBF,GAAU+E,YAAYlN,GAyItBmI,GAAUmE,KAAK1J,EAAQiE,iBAAiBgF,SAAStL,GACjD4H,GAAUmE,KAAK1J,EAAQkE,eAAe+E,SAASxK,GArI/CnC,EAAEsB,GAAa+L,KAAK,SAASe,GACzB,IAgHWkC,EAASlC,EA7BNkC,EAASlC,EAnFnBkC,EAAUtQ,EAAEQ,MACZ+P,EAASD,EAAQlD,KAAKhL,GACtBoO,EAAYD,EAAOjF,OAGvBgF,EAAQ/B,KAAK,YAAa+B,EAAQhC,KAAK,UA8EzBgC,EA5EDA,GA4EUlC,EA5EDA,IA8EoB,IAAjCpO,EAAEuB,GAAoB+J,QAC/BgF,EAAQ3D,SAAS1L,GAErBwH,GAAkBzI,EAAEuB,GAEpB+O,EAAQ9C,IAAI,SAAUtE,GAAgB,MAEnCxF,EAAQqD,YACPuJ,EAAQ9C,IAAI,cAAe9J,EAAQqD,YAGpCrD,EAAQsD,eACPsJ,EAAQ9C,IAAI,iBAAkB9J,EAAQsD,oBAGG,IAAlCtD,EAAQoD,cAAcsH,IAC7BkC,EAAQ9C,IAAI,mBAAoB9J,EAAQoD,cAAcsH,SAGpB,IAA3B1K,EAAQQ,QAAQkK,IACvBkC,EAAQhC,KAAK,cAAe5K,EAAQQ,QAAQkK,IAOjCkC,EAxGDA,EAwGUlC,EAxGDA,OAyGe,IAA3B1K,EAAQQ,QAAQkK,IAEpBkC,EAAQ3M,SAAS1C,IAChBwP,GAAmB/M,EAAQQ,QAAQkK,GAAQA,GAKhD1K,EAAQO,MAAQP,EAAQkB,MAAQ5E,EAAE0D,EAAQO,MAAMyM,QAAQ/P,GAAa2K,QACpEtL,EAAE0D,EAAQO,MAAM0M,SAAS7M,GA/GrB0M,EAAY,EA+BxB,SAAqBF,EAASC,EAAQC,GAClC,IA+FuBF,EA/FnBM,EAA0B,IAAZJ,EACdK,EAAa,IAAML,EAEvBD,EAAOO,QAAQ,eAAiBtO,EAAmB,QACnD+N,EAAOQ,SAASC,KAAK,eAAiB1O,EAAiB,QAEvDgO,EAAQlD,KAAK3K,GAAsB+K,IAAI,QAASoD,EAAc,KAE3DJ,EAAY,IACR9M,EAAQiD,iBAsFQ2J,EArFGA,GAsFlBlD,KAAK7K,GAAoB0O,MAAM,eAAiBhO,EAAoB,uBAAyBG,EAAoB,YAE3F,QAA3BM,EAAQkD,oBACP0J,EAAQlD,KAAK/J,GAAuBmK,IAAI,eAAgB,uCAAuC9J,EAAQkD,mBACvG0J,EAAQlD,KAAKlK,GAAuBsK,IAAI,eAAgB,eAAgB9J,EAAQkD,kBAAoB,6BAGpGlD,EAAQ2B,gBACRiL,EAAQlD,KAAKlK,GAAuBgO,QA3FjCxN,EAAQc,kBAqnDnB,SAA6B8L,EAASE,GAClCF,EAAQa,OAAO,eAAiBxO,EAAa,qBAC7C,IAAIyO,EAAMd,EAAQlD,KAAKxK,GAGvBwO,EAAIzE,SAASjJ,EAAQe,mBAErB,IAAI,IAAI4M,EAAE,EAAGA,EAAGb,EAAWa,IACvBD,EAAIhE,KAAK,MAAM+D,OAAO,0CAI1BC,EAAI5D,IAAI,cAAe,IAAO4D,EAAIzD,QAAQ,EAAK,MAE/CyD,EAAIhE,KAAK,MAAMkE,QAAQlE,KAAK,KAAKT,SAAS1L,GAloDlCsQ,CAAoBjB,EAASE,IAIrCD,EAAOlD,KAAK,SAASe,GACjBpO,EAAEQ,MAAMgN,IAAI,QAASqD,EAAa,KAE/BnN,EAAQmD,kBACP2K,GAAcxR,EAAEQ,SAIxB,IAAIiR,EAAgBnB,EAAQlD,KAAK/K,GAI7BoP,EAAcnG,SAAyD,IAA7CtL,EAAEuB,GAAoB6M,MAAM9M,IAAoE,IAA7CtB,EAAEuB,GAAoB6M,MAAM9M,IAAgD,IAA1BmQ,EAAcrD,SAC7IsD,GAAsBD,EAAe,YAErClB,EAAOoB,GAAG,GAAGhF,SAAS1L,GAhElB2Q,CAAYtB,EAASC,EAAQC,GAE1B9M,EAAQmD,kBACP2K,GAAclB,KAMvB5M,EAAQuD,eAAiBvD,EAAQkB,MAChC5E,EAAE0D,EAAQuD,eAAe0J,SAAS7M,GAInCJ,EAAQU,YAgIf,WACIN,EAAMqN,OAAO,YAAcrP,EAAc,qBACzC,IAAIsP,EAAMpR,EAAE+B,GAEZqP,EAAIzE,SAAS,WACT,OAAOjJ,EAAQa,kBAAoBrC,EAAsB,IAAMwB,EAAQW,mBAAqBX,EAAQW,qBAGxG,IAAK,IAAIgN,EAAI,EAAGA,EAAIrR,EAAEsB,GAAagK,OAAQ+F,IAAK,CAC5C,IAAIQ,EAAO,GACPnO,EAAQQ,QAAQoH,SAChBuG,EAAOnO,EAAQQ,QAAQmN,IAG3B,IAAIS,EAAK,iBAAmBD,EAAO,sBAG/BE,EAAUrO,EAAQY,mBAAmB+M,QAElB,IAAZU,GAAuC,KAAZA,IAClCD,GAAM,eAAiB9P,EAAsB,IAAM0B,EAAQW,mBAAqB,KAAO0N,EAAU,UAGrGD,GAAM,QAENV,EAAIhE,KAAK,MAAM+D,OAAOW,GAI1B9R,EAAE+B,GAAiByL,IAAI,aAAc,IAAOxN,EAAE+B,GAAiBoH,SAAS,EAAK,MAG7EnJ,EAAE+B,GAAiBqL,KAAK,MAAMuE,GAAG3R,EAAEuB,GAAoB6M,MAAM9M,IAAc8L,KAAK,KAAKT,SAAS1L,GA/J1F+Q,GAsKJ/I,GAAUmE,KAAK,qCAAqCC,KAAK,WAQ7D,IAAqB4E,EAASC,EACtBC,EADaF,EAPDjS,EAAEQ,MAOQ0R,EAPD,gBAQrBC,EAAcF,EAAQ3D,KAAK,OAC/B2D,EAAQ3D,KAAK,MAAO6D,GASV,KAAKC,KATmCD,GASb,IAAN,KATkCD,KA3K9DxO,EAAQqC,eACPgE,GAAmBrG,EAAQuC,sBAAsBoM,KAAK3O,GAEtDuL,KA3IJlD,IAAkB,GAClBP,GAAiB9H,EAAQoB,cAAe,YACxCoC,KAGAoL,KAE2B,aAAxBrS,EAASsS,YACRC,KAwDR,IACQtC,EAvDJ5M,EAAQmP,GAAG,OAAQD,IAzBnBH,GA6BA/O,EAEKmP,GAAG,SAAU3F,IAIb2F,GAAG,aAAc1F,IAGjB2F,KAAKC,IAGLC,OAAO5F,IAEZzJ,EAEKsP,QAAQ5F,IAGR6F,MAAM5F,IAGNuF,GAAG,mBAAoB1Q,EAAkB,KAAMgR,IAG/CN,GAAG,mBAAoB5P,EAAqBmQ,IAE5CP,GAAG,QAASxQ,EAAyBgR,IAG1CjT,EAAEsB,GAAamR,GAAG,mBAAoB1P,EAAkBmQ,IAMrDxP,EAAQoC,uBACPvC,EAAUkP,GAAG,wBAAyB/O,EAAQoC,qBAAsB,WAChEiG,IAAkB,KAGtBxI,EAAUkP,GAAG,sBAAuB/O,EAAQoC,qBAAsB,WAC9DiG,IAAkB,OAsS9B,IAAIoH,IAAc,EACdC,GAAa,EAgMbC,GAAc,EACdC,GAAc,EACdC,GAAY,EACZC,GAAY,EA0GZC,IAAW,IAAIC,MAAOC,UAqHtBC,GAAkB,EA4pBlBC,GAAW,EAwIXC,GAAiB5K,GAr2DrB,SAASsC,GAAiBK,EAAOrE,GAEzBqE,GACAsB,GAAa,GAGjB4G,GAAiB,gBAAiBlI,EAAOrE,GAEzC,IAAIyK,EAAUjS,EAAEuB,GAEbmC,EAAQoB,gBAAkBpB,EAAQgB,WACjCb,EAAU2J,KACNO,SAAa,SACb5E,OAAW,SAGfsC,GAAiBV,GAAUrE,cAAe,YAG1CuC,GAAUuE,KACNK,mBAAoB,OACpBC,eAAgB,SAGjBmE,EAAQ3G,QAEP6B,GAAa8E,EAAQrE,WAAWoG,OAIpCnQ,EAAU2J,KACNO,SAAa,UACb5E,OAAW,YAGfsC,IAAiB,EAAO,YAGxBxC,GAAUuE,KACNK,mBAAoB,GACpBC,eAAgB,KAIhBmE,EAAQ3G,QACRzH,EAAUgL,UAAUoD,EAAQrE,WAAWoG,MAQnD,SAASvI,GAAiBI,EAAOrE,GAC7BuM,GAAiB,gBAAiBlI,EAAOrE,GAM7C,SAASkE,GAAkBG,EAAOrE,GAC9BuM,GAAiB,iBAAkBlI,EAAOrE,GAM9C,SAASmE,GAAgBE,EAAOrE,GAC5BuM,GAAiB,eAAgBlI,EAAOrE,GAa5C,SAASsE,GAAuBD,GACzBA,IAwqEP,WACI,IACIoI,EADAC,EAAS,GAGT3T,EAAO4T,iBACPF,EAAoB,oBAEpBA,EAAoB,cACpBC,EAAS,MAIb,IAAIE,EAAU,YAAanU,EAASmP,cAAc,OAAS,QACjDnP,EAASoU,eAAiB5T,EAAY,aACtC,iBAGI,kBAAX2T,EACCnU,EAAUgU,GAAoBC,EAAS,sBAAuBI,IAAmB,GAKjFrU,EAAUgU,GAAoBC,EAASE,EAASE,IAAmB,GA9rEnEC,GAssEJtL,GACKwJ,GAAG,YAAa+B,IAChB/B,GAAG,UAAWgC,MA/CfxU,EAASkU,kBACTlU,EAASyU,oBAAoB,aAAcJ,IAAmB,GAC9DrU,EAASyU,oBAAoB,QAASJ,IAAmB,GACzDrU,EAASyU,oBAAoB,sBAAuBJ,IAAmB,IAEvErU,EAAS0U,YAAY,eAAgBL,IAiDzCrL,GACK4D,IAAI,YAAa2H,IACjB3H,IAAI,UAAW4H,KAnsExB,SAAS1I,GAAkBF,EAAO+I,QACL,IAAfA,GACNA,EAAaA,EAAWC,QAAQ,KAAK,IAAI1G,MAAM,KAE/CnO,EAAEqN,KAAKuH,EAAY,SAAUxG,EAAO0G,GAChCC,GAAmBlJ,EAAOiJ,EAAW,SAIzCC,GAAmBlJ,EAAO,MAAO,KAE9BA,GACCC,IAAuB,IA8rE5BnD,IAAiBI,MACbrF,EAAQoB,eACPhB,EAAM+I,IAAIlC,GAAOC,WAAW6H,GAAG9H,GAAOC,UAAWoK,IAGrDhV,EAAEW,GACGkM,IAAIlC,GAAOE,YAAY4H,GAAG9H,GAAOE,WAAYoK,IAC7CpI,IAAIlC,GAAOC,WAAW6H,GAAG9H,GAAOC,UAAWsK,OAlsE5CpJ,IAAuB,IA0sE5BnD,IAAiBI,MACbrF,EAAQoB,eACPhB,EAAM+I,IAAIlC,GAAOC,WAGrB5K,EAAEW,GACGkM,IAAIlC,GAAOE,YACXgC,IAAIlC,GAAOC,cAxsExB,SAASoB,GAAqBH,EAAO+I,QACR,IAAfA,GACNA,EAAaA,EAAWC,QAAQ,KAAK,IAAI1G,MAAM,KAE/CnO,EAAEqN,KAAKuH,EAAY,SAAUxG,EAAO0G,GAChCC,GAAmBlJ,EAAOiJ,EAAW,SAGzCC,GAAmBlJ,EAAO,MAAO,KACjCnI,EAAQ8C,kBAAoBqF,GAOpC,SAASI,KACL,IAAIkJ,EAAOnV,EAAEuB,GAAoB4T,KAAK7T,GAGjC6T,EAAK7J,SAAW5H,EAAQ0B,UAAW1B,EAAQ4B,qBAC5C6P,EAAOnV,EAAEsB,GAAa8T,QAGtBD,EAAK7J,QACL+J,GAAWF,EAAM,MAAM,GAO/B,SAASjJ,KACL,IAAIoJ,EAAOtV,EAAEuB,GAAoB+T,KAAKhU,GAGlCgU,EAAKhK,SACJ5H,EAAQyB,aAAczB,EAAQ4B,qBAC/BgQ,EAAOtV,EAAEsB,GAAagQ,SAGvBgE,EAAKhK,QACJ+J,GAAWC,EAAM,MAAM,GAQ/B,SAASnJ,GAAaoJ,EAAeC,GACjC9J,GAAmB,EAAG,YACtBU,GAAOmJ,EAAeC,GACtB9J,GAAmBX,GAAUlG,eAAgB,YAOjD,SAASuH,GAAOmJ,EAAeC,GAC3B,IAAIC,EAAUC,GAAmBH,QAEN,IAAhBC,EACPG,GAAmBJ,EAAeC,GAC7BC,EAAQnK,OAAS,GACtB+J,GAAWI,GAQnB,SAASpJ,GAAeiE,GACpBsF,GAAU,QAAStF,GAOvB,SAAShE,GAAcgE,GACnBsF,GAAU,OAAQtF,GAMtB,SAAS/D,GAAQsJ,GACb,IAAG5M,GAAUtF,SAAS7C,GAAtB,CAEAsI,IAAa,EAEbF,GAAgB5F,EAAQ6F,SAExBnJ,EAAEsB,GAAa+L,KAAK,WAChB,IAAIyI,EAAa9V,EAAEQ,MAAM4M,KAAK7K,GAC1BgO,EAASvQ,EAAEQ,MAAM4M,KAAKhL,GAGvBsB,EAAQmD,kBACP7G,EAAEQ,MAAM4M,KAAKzL,GAAgB6L,IAAI,SAAUuI,GAAe/V,EAAEQ,OAAS,MAGzER,EAAEQ,MAAMgN,IAAI,SAAUtE,GAAgB,MAGlCqH,EAAOjF,OAAS,GAChB0K,GAAgBF,EAAYA,EAAW1I,KAAK/K,MAIjDqB,EAAQqC,gBACPgE,GAAiBkM,wBAGrB,IACIC,EADgBlW,EAAEuB,GACW6M,MAAM9M,GAGpC4U,GAEC/J,GAAa+J,EAAe,GAGhC9M,IAAa,EACbpJ,EAAEmW,WAAYzS,EAAQsE,cAAiB6N,GAAYnS,EAAQsE,YAAYoO,KAAKnN,IAC5EjJ,EAAEmW,WAAYzS,EAAQuE,gBAAmB4N,GAAYnS,EAAQuE,aAAamO,KAAKnN,KAOnF,SAASuD,GAAc6J,GACnB,IAAIC,EAAexS,EAAMH,SAAS/C,GAE/ByV,EACKC,IACA9K,IAAiB,EAAO,YACxBG,IAAgB,EAAO,YACvB3L,EAAE+B,GAAiBmP,OACnBpN,EAAM6I,SAAS/L,GACfZ,EAAEmW,WAAYzS,EAAQ0E,kBAAqB1E,EAAQ0E,gBAAgBgO,KAAMnN,GAAWoN,IAGpFC,IACJ9K,GAAiBT,GAAUjG,cAAe,YAC1C6G,GAAgBZ,GAAUjG,cAAe,YACzC9E,EAAE+B,GAAiBwU,OACnBzS,EAAMkK,YAAYpN,GAClBZ,EAAEmW,WAAYzS,EAAQ0E,kBAAqB1E,EAAQ0E,gBAAgBgO,KAAMnN,GAAWoN,IAiX5F,SAASpH,KACL,IAsBIuH,EAtBAlG,EAAUtQ,EAAEuB,GAEhB+O,EAAQ3D,SAASxL,GAEjBsV,GAASnG,GACToG,GAAUpG,GAEP5M,EAAQqC,gBACPrC,EAAQuC,sBAAsB4B,eAc9B2O,EAAqBd,GAAmBiB,KAAgBrG,WAC9BkG,EAAmBlL,QAAUkL,EAAmBpI,UAAY3F,GAAgB2F,UAXtGpO,EAAEmW,WAAYzS,EAAQmE,YAAenE,EAAQmE,UAAUuO,KAAK9F,EAASA,EAAQ/B,KAAK,UAAY+B,EAAQlC,MAAM9M,GAAe,GAG/HtB,EAAEmW,WAAYzS,EAAQqE,cAAiBrE,EAAQqE,YAAYqO,KAAKnN,IAgBpE,SAAS6D,KACL,IAAI8J,EAsIoBC,EACpB/B,EArIJ,IAAIpR,EAAQoB,eAAiBpB,EAAQgB,UAAU,CAC3C,IAAImS,EAAgBvT,EAAQuL,YACxBiI,GAmIJhC,GADoB+B,EAlIqBA,GAmIbzD,GAAa,OAAS,KAEtDA,GAAayD,EAGbjD,GAAkBiD,EAEX/B,GAzICiC,EAAsB,EACtBC,EAAaH,EAAiBvT,EAAQ6F,SAAW,EACjD8N,EAAanT,EAAMqF,SAAW7F,EAAQ6F,WAAa0N,EACnD3G,EAAYjQ,EAASiX,iBAAiB5V,GAG1C,GAAG2V,EACCF,EAAsB7G,EAAS5E,OAAS,OAGvC,GAAIuL,EAML,IAAK,IAAIxF,EAAI,EAAGA,EAAInB,EAAS5E,SAAU+F,EAAG,CACxBnB,EAASmB,GAGX8F,WAAaH,IAErBD,EAAsB1F,QAX9B0F,EAAsB,EA2B1B,GA8ER,SAAgCK,GAC5B,IAAIpD,EAAMhU,EAAEuB,GAAoBqM,WAAWoG,IACvCqD,EAASrD,EAAM1Q,EAAQ6F,SAE3B,GAAe,MAAZiO,EACC,OAAOC,GAAW/T,EAAQuL,YAAcvL,EAAQ6F,SAEpD,OAAO6K,GAAO1Q,EAAQuL,YAhGfyI,CAAuBR,KAClB9W,EAAEuB,GAAoBoC,SAASxC,IAC/BnB,EAAEuB,GAAoBoL,SAASxL,GAAYoW,WAAWvJ,YAAY7M,MAK1EyV,EAAiB5W,EAAEkQ,GAAUyB,GAAGoF,IAIbpT,SAAS1C,GAAQ,CAChCkS,IAAc,EACd,IAMIqE,EACAC,EAPAC,EAAiB1X,EAAEuB,GACnBoW,EAAsBD,EAAetJ,MAAM9M,GAAe,EAC1DsW,EAAYC,GAAajB,GACzBkB,EAAclB,EAAerI,KAAK,UAClC2H,EAAeU,EAAexI,MAAM9M,GAAe,EACnDyW,EAAcnB,EAAexJ,KAAK/K,GAInC0V,EAAYzM,SACXmM,EAAkBM,EAAYxJ,KAAK,UACnCiJ,EAAaO,EAAY3J,SAG1B9E,KACCsN,EAAejK,SAAS1L,GAAQsW,WAAWvJ,YAAY/M,GAEvDjB,EAAEmW,WAAYzS,EAAQoE,UAAapE,EAAQoE,QAAQsO,KAAMsB,EAAgBC,EAAqBzB,EAAc0B,GAC5G5X,EAAEmW,WAAYzS,EAAQmE,YAAenE,EAAQmE,UAAUuO,KAAMQ,EAAgBkB,EAAY5B,GAEzF8B,GAAUN,GACVjB,GAASG,GACTF,GAAUE,GAEVnG,GAAmBqH,EAAY5B,EAAe,GAE3CxS,EAAQQ,QAAQoH,SAEfhD,EAAsBwP,GAE1BG,GAAST,EAAYC,EAAiBK,EAAY5B,IAItDtJ,aAAazC,IACbA,GAAW+N,WAAW,WAClB/E,IAAc,GACf,KAGJzP,EAAQqB,eAEP6H,aAAaxC,IAEbA,GAAY8N,WAAW,WAEhBxU,EAAQqB,cAGP/E,EAAEuB,GAAoB4W,eAAiBjP,IAEvCnE,MAELrB,EAAQsB,qBAQvB,SAASD,KAEFuE,KAGCF,IAAa,EAEbiM,GAAWrV,EAAEuB,IACb6H,IAAa,GAmCrB,SAASgP,GAAU5Q,GACf,GAAKgC,GAAgBC,EAAEjC,GAAvB,CAIA,IAAI6Q,EAA0B,SAAT7Q,EAAmB0E,GAAkBD,GAE1D,GAAGvI,EAAQqC,eAAe,CACtB,IAAIuS,EAAa5U,EAAQuC,sBAAsBqS,WAAWtY,EAAEuB,IACxDgX,EAAkB,SAAT/Q,EAAmB,SAAW,MAE3C,GAAG8Q,EAAWhN,OAAS,EAAG,CAEtB,IAAG5H,EAAQuC,sBAAsBuS,WAAWD,EAAOD,GAG/C,OAAO,EAFPD,SAMJA,SAIJA,KAOR,SAASrD,GAAgByD,GACrB,IAAIC,EAAID,EAAME,cACXjV,EAAQoB,eAAiB8T,GAAcF,IAEtCD,EAAMI,iBAed,SAAS3D,GAAiBuD,GACtB,IAAIC,EAAID,EAAME,cACVG,EAAgB9Y,EAAE0Y,EAAEK,QAAQrI,QAAQpP,GAGxC,GAAIsX,GAAcF,GAAK,CAEhBhV,EAAQoB,eAEP2T,EAAMI,iBAGV,IAAIG,EAAcC,GAAcP,GAEhCnF,GAAYyF,EAAYE,EACxB1F,GAAYwF,EAAY/N,EAGpB6N,EAAc1L,KAAK7K,GAAoB+I,QAAUpL,EAAKiZ,IAAI7F,GAAcE,IAActT,EAAKiZ,IAAI9F,GAAcE,KAGxG7K,IAAexI,EAAKiZ,IAAI7F,GAAcE,IAAclQ,EAAQ8V,aAAe,IAAM1V,EAAQ2C,mBACtFiN,GAAcE,GACXhK,GAAgBC,EAAEI,OACjBwC,GAAeyM,GAGhBtP,GAAgBC,EAAEG,MACjB0C,GAAcwM,IAOtBpV,EAAQoB,eAAiBwE,IAGzBpJ,EAAKiZ,IAAI9F,GAAcE,IAAcjQ,EAAQ6F,SAAW,IAAMzF,EAAQ2C,mBAClEgN,GAAcE,GACd6E,GAAU,QACH7E,GAAYF,IACnB+E,GAAU,QAW9B,SAASQ,GAAcF,GAEnB,YAAgC,IAAlBA,EAAEW,aAAgD,SAAjBX,EAAEW,YAMrD,SAASpE,GAAkBwD,GACvB,IAAIC,EAAID,EAAME,cAOd,GAJGjV,EAAQqB,cACPlB,EAAUyV,OAGXV,GAAcF,GAAG,CAChB,IAAIM,EAAcC,GAAcP,GAChCrF,GAAc2F,EAAYE,EAC1B5F,GAAc0F,EAAY/N,GAOlC,SAASsO,GAAWC,EAAUC,GAM1B,IALA,IAAIC,EAAM,EAGNC,EAAeH,EAASI,MAAM1Z,EAAK2Z,IAAIL,EAASlO,OAASmO,EAAQ,IAE7DpI,EAAI,EAAGA,EAAIsI,EAAarO,OAAQ+F,IACpCqI,GAAYC,EAAatI,GAG7B,OAAOnR,EAAK4Z,KAAKJ,EAAID,GAWzB,SAASnF,GAAkBoE,GACvB,IAAIqB,GAAU,IAAIrG,MAAOC,UACrBqG,EAAiBha,EAAEoB,GAAgBuC,SAAS9B,GAGhD,GAAG6B,EAAQoB,gBAAkB0D,KAAmBwR,EAAe,CAG3D,IAAInO,GADJ6M,EAAIA,GAAKnY,EAAOkY,OACFwB,aAAevB,EAAEwB,SAAWxB,EAAEyB,OACxCC,EAAQla,EAAK2Z,KAAK,EAAG3Z,EAAKma,IAAI,EAAGxO,IAEjCyO,OAA+C,IAAlB5B,EAAE6B,kBAAmD,IAAb7B,EAAE8B,OACvEC,EAAyBva,EAAKiZ,IAAIT,EAAE6B,aAAera,EAAKiZ,IAAIT,EAAEuB,aAAiB/Z,EAAKiZ,IAAIT,EAAE8B,QAAWta,EAAKiZ,IAAIT,EAAEwB,UAAYI,EAG7H/Q,GAAW+B,OAAS,KACnB/B,GAAWmR,QAIfnR,GAAWoR,KAAKza,EAAKiZ,IAAItN,IAGtBnI,EAAQgB,YACPgU,EAAEG,eAAiBH,EAAEG,iBAAmBH,EAAEkC,aAAc,GAI5D,IAAIC,EAAWd,EAAQtG,GAUvB,GATAA,GAAWsG,EAIRc,EAAW,MAEVtR,OAGDD,GACkBiQ,GAAWhQ,GAAY,KACpBgQ,GAAWhQ,GAAY,KAItBkR,GAGbrC,GADAgC,EAAQ,EACE,OAIA,MAKtB,OAAO,EAGR1W,EAAQqB,cAEPlB,EAAUyV,OAQlB,SAAS1D,GAAUd,EAAWxE,GAC1B,IACIC,QADmC,IAAZD,EAA0BtQ,EAAEuB,GAAsB+O,GAClDlD,KAAK7K,GAC5BiO,EAAYD,EAAOnD,KAAKhL,GAAWkJ,OAGvC,MAAKiF,EAAOjF,QAAU5C,IAAe8H,EAAY,GAAjD,CAIA,IAAIsK,EAAevK,EAAOnD,KAAK/K,GAC3BoT,EAAU,KASd,KANIA,EADa,SAAdX,EACWgG,EAAa3F,KAAK/S,GAElB0Y,EAAaxF,KAAKlT,IAIpBkJ,OAAO,CAEf,IAAK5H,EAAQ2B,eAAgB,OAGzBoQ,EADa,SAAdX,EACWgG,EAAavD,SAAS,SAEtBuD,EAAavD,SAAS,UAIxC7O,IAAc,EAEdsN,GAAgBzF,EAAQkF,EAASX,IAOrC,SAASiG,KACL/a,EAAEqC,GAAkBgL,KAAK,WACrBqE,GAAsB1R,EAAEQ,MAAO,cA6CvC,SAAS6U,GAAWpD,EAAS+I,EAAUC,GACnC,QAAsB,IAAZhJ,EAAV,CAEA,IAvC4BA,EACxBiJ,EAGAtN,EACAuN,EACAC,EACA7U,EAiCAkR,EACAD,EAGA6D,GACApJ,QAASA,EACT+I,SAAUA,EACVC,aAAcA,EACdK,MA/CAJ,GADwBjJ,EAuCMA,GAtCPrE,WAGvBA,EAAWsN,EAAalH,IACxBmH,EAAmBD,EAAalH,IAAMJ,GACtCwH,EAAgBxN,EAAW1E,GAAgB+I,EAAQkG,cACnD5R,EAAyB7C,EAAQ6C,uBAGlC0L,EAAQkG,cAAgBjP,IAEnBiS,GAAoB5U,IAAqD,WAA3BA,IAC9CqH,EAAWwN,IAKXD,GAAoB/R,IAAc6I,EAAQsJ,GAAG,kBAEjD3N,EAAWwN,GASfxH,GAAkBhG,EACXA,GAmBHgK,UAAWC,GAAa5F,GACxB6F,WAAY7F,EAAQ1D,KAAK,UACzB2H,aAAcjE,EAAQ7D,MAAM9M,GAC5ByW,YAAa9F,EAAQ7E,KAAK/K,GAC1ByW,cAAe9Y,EAAEuB,GACjBmW,eAAgB1X,EAAEuB,GAAoB6M,MAAM9M,GAAe,EAI3Dka,gBAAiBpS,IAIrB,KAAIiS,EAAEvC,cAAcyC,GAAGtJ,KAAa7I,IAAgB1F,EAAQgB,WAAapB,EAAQuL,cAAgBwM,EAAEC,OAASrJ,EAAQtO,SAAS/B,IAA7H,CAQA,GANGyZ,EAAEtD,YAAYzM,SACbmM,EAAkB4D,EAAEtD,YAAYxJ,KAAK,UACrCiJ,EAAa6D,EAAEtD,YAAY3J,SAI5BpO,EAAEmW,WAAWzS,EAAQoE,WAAauT,EAAEG,gBAAgB,CACnD,IAAI1G,EAAYuG,EAAEzD,UAOlB,QAJ2B,IAAjBqD,IACNnG,EAAYmG,EAAe,KAAO,SAG0D,IAA7FvX,EAAQoE,QAAQsO,KAAKiF,EAAEvC,cAAeuC,EAAE3D,eAAiB2D,EAAEnF,aAAe,EAAIpB,GAC7E,OAKJpR,EAAQoB,eAAiBpB,EAAQ4B,yBAAkD,IAApB+V,EAAc,gBAC1EA,EAAEJ,cAA+B,MAAfI,EAAEzD,WACtByD,EAAEJ,cAA+B,QAAfI,EAAEzD,aAErByD,EAuGR,SAAgCA,GAEvBA,EAAEJ,aAMHjb,EAAEuB,GAAoBka,OAAOJ,EAAEvC,cAAc4C,QAAQpa,IAJrDtB,EAAEuB,GAAoB0P,MAAMoK,EAAEvC,cAAc6C,QAAQra,GAAa2M,MAAM2N,WAyB3E,OAjBAzO,GAAanN,EAAEuB,GAAoBqM,WAAWoG,KAG9C+G,KAGAM,EAAEQ,mBAAqBR,EAAEvC,cAGzBuC,EAAEC,KAAOD,EAAEpJ,QAAQrE,WAAWoG,IAC9BqH,EAAEzD,UAAYC,GAAawD,EAAEpJ,SAI7BoJ,EAAE3D,eAAiB2D,EAAEvC,cAAc1K,MAAM9M,GAAe,EACxD+Z,EAAEnF,aAAemF,EAAEpJ,QAAQ7D,MAAM9M,GAE1B+Z,EApICS,CAAuBT,IAI3BA,EAAEG,iBACFxD,GAAUqD,EAAEvC,eAGbpV,EAAQqC,gBACPrC,EAAQuC,sBAAsB8V,cAGlC9J,EAAQtF,SAAS1L,GAAQsW,WAAWvJ,YAAY/M,GAChDwV,GAASxE,GAENvO,EAAQqC,gBACPrC,EAAQuC,sBAAsB6B,UAKlCwB,IAAY,EAEZ2O,GAAST,EAAYC,EAAiB4D,EAAEvD,WAAYuD,EAAEnF,cAc1D,SAAyBmF,GAErB,GAAI3X,EAAQkB,MAAQlB,EAAQoB,gBAAkBpB,EAAQgB,UAAW,CAI7D,IAAIsX,EAAc,qBAAuB9b,EAAK+b,MAAMZ,EAAEC,MAAQ,WAC9DY,GAAmBF,GAAa,GAI7BtY,EAAQmB,gBACP+H,aAAa3C,IACbA,GAAsBiO,WAAW,WAC7BiE,GAAkBd,IACnB3X,EAAQmB,iBAEXsX,GAAkBd,OAKtB,CACA,IAAIe,EA0BZ,SAA2Bf,GACvB,IAAIgB,KAED3Y,EAAQoB,gBAAkBpB,EAAQgB,WACjC2X,EAAO3Y,SAAYsQ,KAAQqH,EAAEC,MAC7Be,EAAOpK,QAAUtR,IAEjB0b,EAAO3Y,SAAYmL,UAAawM,EAAEC,MAClCe,EAAOpK,QAAU,cAGrB,OAAOoK,EArCkBC,CAAkBjB,GAEvCrb,EAAEoc,EAAenK,SAASsK,QACtBH,EAAe1Y,QACnBA,EAAQmB,eAAgBnB,EAAQuB,QAAQuX,UAAUC,KAAK,WAChD/Y,EAAQgB,UAQPwT,WAAW,WACPiE,GAAkBd,IACpB,IAEFc,GAAkBd,MApD9BqB,CAAgBrB,GAGhB/S,EAAsB+S,EAAEvD,WAGxBrH,GAAmB4K,EAAEvD,WAAYuD,EAAEnF,gBAmIvC,SAASiG,GAAmBd,GAxB5B,IAA4CA,GAAAA,EAyBNA,GAtB3BQ,oBAAuBR,EAAEQ,mBAAmBvQ,SAI/C+P,EAAEJ,aACFjb,EAAEwB,GAAmBia,OAAOJ,EAAEQ,oBAG9B7b,EAAEyB,GAAkBwP,MAAMoK,EAAEQ,oBAGhC1O,GAAanN,EAAEuB,GAAoBqM,WAAWoG,KAG9C+G,MAWA/a,EAAEmW,WAAWzS,EAAQmE,aAAewT,EAAEG,iBAAmB9X,EAAQmE,UAAUuO,KAAKiF,EAAEpJ,QAASoJ,EAAEvD,WAAauD,EAAEnF,aAAe,GAExHxS,EAAQqC,gBACPrC,EAAQuC,sBAAsB4B,YAG9BwT,EAAEG,iBACF9E,GAAU2E,EAAEpJ,SAGhBoJ,EAAEpJ,QAAQtF,SAASxL,GAAYoW,WAAWvJ,YAAY7M,GAEtDmI,IAAY,EAEZtJ,EAAEmW,WAAWkF,EAAEL,WAAaK,EAAEL,SAAS5E,KAAK5V,MAOhD,SAAS8M,GAAO2E,EAAS0K,GACrB1K,EACK3D,KAAKqO,EAAW1K,EAAQ1D,KAAKoO,IAC7BC,WAAW,QAAUD,GAM9B,SAASlG,GAAShB,GAKd,IACIxD,EALCvO,EAAQ2E,aAIDwU,GAAkBpH,GAGxBrI,KAAK,8HAA8HC,KAAK,WAU1I,GATA4E,EAAUjS,EAAEQ,MAEZR,EAAEqN,MAAM,MAAO,UAAW,SAASe,EAAO5G,GACtC,IAAImV,EAAY1K,EAAQ3D,KAAK,QAAU9G,QACf,IAAdmV,GAA6BA,GACnCrP,GAAO2E,EAASzK,KAIrByK,EAAQsJ,GAAG,UAAU,CACpB,IAAIuB,EAAa7K,EAAQvB,QAAQ,SAASpF,OAAS,QAAU,QAC7D2G,EAAQvB,QAAQoM,GAAY7O,IAAI,GAAG8O,UAQ/C,SAASrG,GAAUjB,GACf,IAAIuH,EAAQH,GAAkBpH,GAG9BuH,EAAM5P,KAAK,gBAAgBC,KAAK,WAC5B,IAAI4E,EAAUjS,EAAEQ,MAAMyN,IAAI,GAEtBgE,EAAQgL,aAAa,kBAA4C,mBAAjBhL,EAAQiL,MACxDjL,EAAQiL,SAKhBF,EAAM5P,KAAK,qCAAqCC,KAAK,WACjD,IAAI4E,EAAUjS,EAAEQ,MAAMyN,IAAI,GAErBgE,EAAQgL,aAAa,kBACtBE,GAAYlL,GAIhBA,EAAQmL,OAAS,WACRnL,EAAQgL,aAAa,kBACtBE,GAAYlL,MAS5B,SAASkL,GAAYlL,GACjBA,EAAQoL,cAAcC,YAAY,mDAAoD,KAM1F,SAAStF,GAAUvC,GACf,IAAIuH,EAAQH,GAAkBpH,GAG9BuH,EAAM5P,KAAK,gBAAgBC,KAAK,WAC5B,IAAI4E,EAAUjS,EAAEQ,MAAMyN,IAAI,GAErBgE,EAAQgL,aAAa,qBAAgD,mBAAlBhL,EAAQsL,OAC5DtL,EAAQsL,UAKhBP,EAAM5P,KAAK,qCAAqCC,KAAK,WACjD,IAAI4E,EAAUjS,EAAEQ,MAAMyN,IAAI,GAEtB,wBAAwBmE,KAAKpS,EAAEQ,MAAM8N,KAAK,UAAY2D,EAAQgL,aAAa,qBAC3Ejd,EAAEQ,MAAMyN,IAAI,GAAGoP,cAAcC,YAAY,oDAAoD,OAQzG,SAAST,GAAkBpH,GACvB,IAAI+H,EAAQ/H,EAAQrI,KAAK/K,GAKzB,OAJImb,EAAMlS,SACNmK,EAAUzV,EAAEwd,IAGT/H,EAMX,SAASjD,KACL,IAAItO,EAAWyS,KACXpB,EAAgBrR,EAAQoM,QACxBkF,EAActR,EAAQsZ,MAEvBjI,IACI7R,EAAQ+C,cACPkP,GAAmBJ,EAAeC,GAElCrJ,GAAaoJ,EAAeC,IASxC,SAASzI,KACL,IAAIoG,KAAgBzP,EAAQS,YAAY,CACpC,IAAID,EAAUyS,KACVpB,EAAgBrR,EAAQoM,QACxBkF,EAActR,EAAQsZ,MAGtBC,OAAoD,IAAxBnV,EAC5BoV,OAAoD,IAAxBpV,QAA8D,IAAhBkN,IAAgC9M,GAE3G6M,GAAiBA,EAAcjK,SAIzBiK,GAAiBA,IAAkBjN,IAAyBmV,GAC1DC,IACEhV,IAAeH,IAAqBiN,IAEzCG,GAAmBJ,EAAeC,IAOlD,SAASmB,KACL,IAAIrG,EACAkN,EACAG,EAAOpd,EAAOqd,SAASD,KAE3B,GAAGA,EAAKrS,OAAO,CAEX,IAAIuS,EAAgBF,EAAK9I,QAAQ,IAAK,IAAI1G,MAAM,KAG5C2P,EAAgBH,EAAKtP,QAAQ,OAAS,EAE1CiC,EAAUwN,EAAgB,IAAMD,EAAa,GAAKE,mBAAmBF,EAAa,IAElF,IAAIrI,EAAcsI,EAAgBD,EAAa,GAAKA,EAAa,GAC9DrI,GAAeA,EAAYlK,SAC1BkS,EAAQO,mBAAmBvI,IAInC,OACIlF,QAASA,EACTkN,MAAOA,GAKf,SAASvQ,GAAeyL,GACpB9L,aAAavC,IAEb,IAAI2T,EAAgBhe,EAAE,UAClBie,EAAUvF,EAAEwF,MAGhB,GAAe,IAAZD,GAgKP,SAAevF,GACX,IAAIyF,EAAiBzF,EAAE0F,SACnBJ,EAAgBhe,EAAE,UAClB8Y,EAAgB9Y,EAAEuB,GAClBwW,EAAce,EAAc1L,KAAK/K,GAEjCgc,GADmBtG,EAAYzM,OAASyM,EAAce,GACjB1L,KAAKtC,IAAyBwT,IAAI,mBAE3E,SAASC,EAAqB7F,GAE1B,OADAA,EAAEG,iBACKwF,EAAkB/M,QAAQkN,QAIlCR,EAAc1S,OACT0S,EAActN,QAAQnP,EAAoBc,GAAkBiJ,SAC5D0S,EAAgBO,EAAqB7F,IAMzC6F,EAAqB7F,KAKrByF,GAAkBH,EAAczC,GAAG8C,EAAkBjJ,SACrD+I,GAAkBH,EAAczC,GAAG8C,EAAkB/M,WAErDoH,EAAEG,iBA7LF4F,CAAM/F,QAGL,IAAIsF,EAAczC,GAAG,cAAgByC,EAAczC,GAAG,WAAayC,EAAczC,GAAG,WAC3C,SAA1CyC,EAAc1P,KAAK,oBAA2E,KAA1C0P,EAAc1P,KAAK,oBACvE5K,EAAQ8C,mBAAqB9C,EAAQoB,cAAc,CAIhD9E,EAAE0e,QAAQT,GADM,GAAI,GAAI,GAAI,GAAI,MACG,GAClCvF,EAAEG,iBAGNrQ,GAAiBkQ,EAAEiG,QAEnBtU,GAAY6N,WAAW,YA0E/B,SAAmBQ,GACf,IAAIkG,EAAelG,EAAE0F,SAGrB,IAAI9U,KAAc,GAAG,IAAI+E,QAAQqK,EAAEwF,OAAS,EACxC,OAGJ,OAAQxF,EAAEwF,OAEN,KAAK,GACL,KAAK,GACE1U,GAAgBM,EAAEJ,IACjBuC,KAEJ,MAGJ,KAAK,GACD,GAAG2S,GAAgBpV,GAAgBM,EAAEJ,GAAG,CACpCuC,KACA,MAGR,KAAK,GACL,KAAK,GACEzC,GAAgBM,EAAEH,MACjBuC,KAEJ,MAGJ,KAAK,GACE1C,GAAgBM,EAAEJ,IACjB0C,GAAO,GAEX,MAGJ,KAAK,GACG5C,GAAgBM,EAAEH,MAClByC,GAAQpM,EAAEsB,GAAagK,QAE3B,MAGJ,KAAK,GACE9B,GAAgBM,EAAEF,MACjB0C,KAEJ,MAGJ,KAAK,GACE9C,GAAgBM,EAAED,OACjBwC,KAEJ,MAEJ,WApIIwS,CAAUnG,IACZ,MAIV,SAASzF,KACLjT,EAAEQ,MAAM2U,OAAO2J,QAAQ,SAI3B,SAAS5R,GAAawL,GACfrP,KACCb,GAAiBkQ,EAAEiG,SAK3B,SAASnK,GAAiBkE,GAEP,GAAXA,EAAEwF,QACFrK,GAAW6E,EAAEqG,MACb9V,GAAUwJ,GAAG,YAAauM,KAKlC,SAASvK,GAAeiE,GAEL,GAAXA,EAAEwF,OACFjV,GAAU4D,IAAI,aAKtB,SAASqG,KACL,IAAI5C,EAAUtQ,EAAEQ,MAAMkQ,QAAQpP,GAE1BtB,EAAEQ,MAAMmD,SAASX,GACdwG,GAAgBC,EAAEG,MACjB0C,GAAcgE,GAGf9G,GAAgBC,EAAEI,OACjBwC,GAAeiE,GAM3B,SAASqC,KACLtJ,IAAkB,EAClBb,IAAiB,EAIrB,SAASuK,GAAqB2F,GAC1BA,EAAEG,iBACF,IAAIzK,EAAQpO,EAAEQ,MAAMuQ,SAAS3C,QAC7BiH,GAAWrV,EAAEsB,GAAaqQ,GAAGvD,IAIjC,SAAS4E,GAAmB0F,GACxBA,EAAEG,iBACF,IAAItI,EAASvQ,EAAEQ,MAAMkQ,QAAQpP,GAAa8L,KAAK7K,GAG/CyT,GAAgBzF,EAFFA,EAAOnD,KAAKhL,GAAWuP,GAAG3R,EAAEQ,MAAMkQ,QAAQ,MAAMtC,UAqHlE,SAAS4Q,GAAiBtG,GACnBpP,KAEKoP,EAAEqG,MAAQlL,IAAYrK,GAAgBC,EAAEC,GACxCuC,KAIIyM,EAAEqG,MAAQlL,IAAYrK,GAAgBC,EAAEE,MAC5CuC,MAGR2H,GAAW6E,EAAEqG,MAMjB,SAAS/I,GAAgBzF,EAAQkF,EAASX,GACtC,IAAIxE,EAAUC,EAAOG,QAAQpP,GACzB+Z,GACA9K,OAAQA,EACRkF,QAASA,EACTX,UAAWA,EACXmK,WAAYxJ,EAAQ7H,WACpB4J,WAAY/B,EAAQrH,QACpBkC,QAASA,EACT4F,aAAc5F,EAAQlC,MAAM9M,GAC5BwW,WAAYxH,EAAQ/B,KAAK,UACzB2Q,UAAW5O,EAAQlD,KAAKxK,GACxB4S,YAAc2J,GAAU1J,GACxB2J,UAAW9O,EAAQlD,KAAK/K,GACxBgd,eAAgB/O,EAAQlD,KAAK/K,GAAkB+L,QAI/CoN,gBAAiBpS,IAErBiS,EAAEiE,UAgON,SAAsBC,EAAWC,GAC7B,GAAID,GAAaC,EACb,MAAO,OAEX,GAAGD,EAAYC,EACX,MAAO,OAEX,MAAO,QAvOOC,CAAapE,EAAEgE,eAAgBhE,EAAE7D,YAG3C6D,EAAEG,kBAEFlS,IAAY,GAGb5F,EAAQyE,eAGHkT,EAAEG,iBAAiC,SAAdH,EAAEiE,WACpBtf,EAAEmW,WAAYzS,EAAQyE,gBAC4G,IAA9HzE,EAAQyE,aAAaiO,KAAMiF,EAAE+D,UAAW/D,EAAEvD,WAAauD,EAAEnF,aAAe,EAAImF,EAAEgE,eAAgBhE,EAAEvG,UAAWuG,EAAE7D,YAC5G9O,IAAc,GAO9B+M,EAAQ9I,SAAS1L,GAAQsW,WAAWvJ,YAAY/M,GAE5Coa,EAAEG,kBACFxD,GAAUqD,EAAE+D,WACZ3I,GAAShB,KAGT/R,EAAQ2B,gBAAkB3B,EAAQiD,gBAElC2J,EAAQlD,KAAKlK,GAAuBwc,OAAsB,IAAfrE,EAAE7D,YAG7ClH,EAAQlD,KAAK/J,GAAuBqc,QAAQjK,EAAQ8F,GAAG,iBAIxDjL,EAAQ3M,SAAS1C,KAAYoa,EAAEG,iBAC9BvD,GAASoD,EAAE7D,WAAY6D,EAAE7F,YAAa6F,EAAEvD,WAAYuD,EAAEnF,cA8B9D,SAA+B3F,EAAQ8K,EAAGsE,GACtC,IAAIV,EAAa5D,EAAE4D,WAEnB,GAAGvb,EAAQkB,KAAK,CACZ,IAAIoX,EAAc,gBAAkB9b,EAAK+b,MAAMgD,EAAWrV,MAAQ,gBAElEgW,GAAarP,EAAOnD,KAAK3K,IAAuB+K,IAAIqS,GAAc7D,IAElE9R,GAAoBgO,WAAW,WAC3ByH,GAAgBG,GAAgBzE,IACjC3X,EAAQmB,eAAgBnB,EAAQuB,aAEnCsL,EAAOgM,SACHwD,WAAa7f,EAAK+b,MAAMgD,EAAWrV,OACpClG,EAAQmB,eAAgBnB,EAAQuB,OAAQ,WAEvC0a,GAAgBG,GAAgBzE,KA3CxC2E,CAAsBzP,EAAQ8K,GAAG,IAIrC,SAASyE,GAAgBzE,GA+CzB,IAAgC6D,EAAW1H,EAAX0H,EA9CL7D,EAAE6D,UA8Cc1H,EA9CH6D,EAAE7D,WA+CtC0H,EAAU9R,KAAKlM,GAAY8M,YAAY/M,GACvCie,EAAU9R,KAAK,MAAMuE,GAAG6F,GAAYpK,KAAK,KAAKT,SAAS1L,GA7CnDoa,EAAEG,kBACFxb,EAAEmW,WAAYzS,EAAQwE,iBAAoBxE,EAAQwE,eAAekO,KAAMiF,EAAE5F,QAAS4F,EAAEvD,WAAauD,EAAEnF,aAAe,EAAImF,EAAE7F,YAAa6F,EAAE7D,YAIvIlO,IAAY,EAEZoN,GAAU2E,EAAE5F,UAIhB/M,IAAc,EAwClB,SAASsE,KAKL,GAHA9F,KAGIyB,GAAe,CACf,IAAIqV,EAAgBhe,EAAEC,EAAS+d,eAG/B,IAAKA,EAAczC,GAAG,cAAgByC,EAAczC,GAAG,WAAayC,EAAczC,GAAG,UAAW,CAC5F,IAAI0E,EAAgB3c,EAAQ6F,SAGxBjJ,EAAKiZ,IAAI8G,EAAgBnM,IAAmB,GAAK5T,EAAK2Z,IAAI/F,GAAgBmM,GAAiB,MAC3F1T,IAAQ,GACRuH,GAAiBmM,SAMzBrT,aAAa5C,IAEbA,GAAWkO,WAAW,WAClB3L,IAAQ,IACT,KAQX,SAASrF,KACL,IAAIgZ,EAAaxc,EAAQwD,YAAcxD,EAAQyD,gBAC3CgZ,EAAczc,EAAQ0D,iBAGtBgZ,EAAuBF,GAAc5c,EAAQ8V,aAAe8G,EAC5DG,EAAwBF,GAAe7c,EAAQ6F,SAAWgX,EAE3DD,GAAcC,EACb3T,GAAc4T,GAAwBC,GAElCH,EACJ1T,GAAc4T,GAEVD,GACJ3T,GAAc6T,GAOtB,SAAST,GAAa3N,GAClB,IAAIrD,EAAa,OAASlL,EAAQmB,eAAiB,MAAQnB,EAAQwB,WAGnE,OADA+M,EAAQjE,YAAYnN,GACboR,EAAQzE,KACXmB,qBAAsBC,EACtBA,WAAcA,IAOtB,SAASJ,GAAgByD,GACrB,OAAOA,EAAQtF,SAAS9L,GA8B5B,SAAS4P,GAAmB6P,EAAQlS,GAVpC,IAA6BmS,EAdJA,EAAMrK,EAcFqK,EAWLD,EAVjB5c,EAAQO,OACPjE,EAAE0D,EAAQO,MAAMmJ,KAAKlM,GAAY8M,YAAY/M,GAC7CjB,EAAE0D,EAAQO,MAAMmJ,KAAK,qBAAqBmT,EAAK,MAAM5T,SAAS1L,IAjB7Csf,EA0BLD,EA1BWpK,EA0BH9H,EAzBrB1K,EAAQU,aACPpE,EAAE+B,GAAiBqL,KAAKlM,GAAY8M,YAAY/M,GAC7Csf,EACCvgB,EAAE+B,GAAiBqL,KAAK,YAAcmT,EAAO,MAAM5T,SAAS1L,GAE5DjB,EAAE+B,GAAiBqL,KAAK,MAAMuE,GAAGuE,GAAc9I,KAAK,KAAKT,SAAS1L,IA2B9E,SAAS4W,GAAapC,GAClB,IAAI8J,EAAYvf,EAAEuB,GAAoB6M,MAAM9M,GACxCke,EAAU/J,EAAQrH,MAAM9M,GAC5B,OAAIie,GAAaC,EACN,OAERD,EAAYC,EACJ,KAEJ,OAiBX,SAAShO,GAAcS,GAEnB,IAAIA,EAAQtO,SAASjB,GAAO,CACxB,IAAI8d,EAAUxgB,EAAE,eAAiB0B,EAAa,QAAQyH,OAAO4M,GAAe9D,IAC5EA,EAAQtF,SAASjK,GAAO+d,UAAUD,IAI1C,SAASzK,GAAe9D,GACpB,IAAIyO,EAAgBxX,GAEpB,GAAGxF,EAAQqD,YAAcrD,EAAQsD,cAAc,CAC3C,IAAIsJ,EAAU2B,EACV3B,EAAQ3M,SAAStC,KACjBiP,EAAU2B,EAAQvB,QAAQpP,IAG9B,IAAIqf,EAAWC,SAAStQ,EAAQ9C,IAAI,gBAAkBoT,SAAStQ,EAAQ9C,IAAI,mBAC3EkT,EAAiBxX,GAAgByX,EAGrC,OAAOD,EAMX,SAASxE,GAAmBF,EAAa6E,GAClCA,EACCjB,GAAa3W,IAEbuF,GAAgBvF,IAGpBA,GAAUuE,IAAIqS,GAAc7D,IAG5B9D,WAAW,WACPjP,GAAU+E,YAAYnN,IACxB,IAMN,SAAS6U,GAAmBH,GACxB,IAAIjF,EAAUrH,GAAUmE,KAAK9L,EAAc,iBAAiBiU,EAAc,MAC1E,IAAIjF,EAAQhF,OAAO,CACf,IAAI4K,OAAwC,IAAlBX,EAAgCA,EAAe,EAAI,EAC7EjF,EAAUtQ,EAAEsB,GAAaqQ,GAAGuE,GAGhC,OAAO5F,EAmBX,SAASqF,GAAmBJ,EAAeC,GACvC,IAAIlF,EAAUoF,GAAmBH,GAGjC,GAAIjF,EAAQhF,OAAZ,CAEA,IAnBsBkK,EAAalF,EAC/BkN,EAkBAA,GAnBkBhI,EAmBOA,GAlBzBgI,GAD+BlN,EAmBOA,GAlBtBlD,KAAKhL,EAAY,iBAAiBoT,EAAY,OACxDlK,SACNkK,OAAqC,IAAhBA,EAA8BA,EAAc,EACjEgI,EAAQlN,EAAQlD,KAAKhL,GAAWuP,GAAG6D,IAGhCgI,GAeHjI,IAAkBjN,GAAwBgI,EAAQ3M,SAAS1C,GAO3D6f,GAAatD,GANbnI,GAAW/E,EAAS,WAChBwQ,GAAatD,MAYzB,SAASsD,GAAatD,GACfA,EAAMlS,QACL0K,GAAgBwH,EAAM9M,QAAQnO,GAAqBib,GA6B3D,SAASvF,GAAST,EAAYhC,EAAasC,EAAY5B,GACnD,IAAI6K,EAAc,GAEfrd,EAAQQ,QAAQoH,SAAW5H,EAAQS,cAG/BqT,QAC0B,IAAfM,IACNiJ,EAAcjJ,QAIQ,IAAhBtC,IACNA,EAAcgC,GAGlBjP,GAAoBiN,EACpBwL,GAAWD,EAAc,IAAMvL,SAGJ,IAAfgC,GACZjP,GAAoBiN,EACpBwL,GAAWlJ,IAKXkJ,GAAWlJ,IAInBxF,KAMJ,SAAS0O,GAAWC,GAChB,GAAGvd,EAAQgD,cACPkX,SAASD,KAAOsD,OAGhB,GAAGtY,IAAiBI,GAChBxI,EAAO2gB,QAAQC,aAAa1gB,EAAWA,EAAW,IAAMwgB,OACvD,CACD,IAAIG,EAAU7gB,EAAOqd,SAASyD,KAAKlT,MAAM,KAAK,GAC9C5N,EAAOqd,SAAS/I,QAASuM,EAAU,IAAMH,IAQrD,SAAS9B,GAAUlN,GACf,IAAIqO,EAASrO,EAAQ1D,KAAK,UACtBH,EAAQ6D,EAAQ7D,QAOpB,YAJqB,IAAXkS,IACNA,EAASlS,GAGNkS,EAMX,SAAShO,KACL,IAAIhC,EAAUtQ,EAAEuB,GACZic,EAAQlN,EAAQlD,KAAK/K,GAErBkT,EAAgB4J,GAAU7O,GAC1BkF,EAAc2J,GAAU3B,GAExB8D,EAAOC,OAAOhM,GAEfiI,EAAMlS,SACLgW,EAAOA,EAAO,IAAM9L,GAIxB8L,EAAOA,EAAKzM,QAAQ,IAAK,KAAKA,QAAQ,IAAI,IAG1C,IAAI2M,EAAU,IAAIC,OAAO,UAAYzgB,EAAiB,cAAe,KACrE8C,EAAM,GAAGoK,UAAYpK,EAAM,GAAGoK,UAAU2G,QAAQ2M,EAAS,IAGzD1d,EAAM6I,SAAS3L,EAAiB,IAAMsgB,GAwJ1C,SAASrI,GAAcP,GACnB,IAAI/N,KAWJ,OATAA,EAAOuO,OAAwB,IAAZR,EAAEqG,QAA0BrG,EAAEqG,OAASrG,EAAEgJ,OAAShJ,EAAEqG,MAAQrG,EAAEiJ,QAAQ,GAAG5C,MAC5FpU,EAAOM,OAAwB,IAAZyN,EAAEgJ,QAA0BhJ,EAAEqG,OAASrG,EAAEgJ,OAAShJ,EAAEgJ,MAAQhJ,EAAEiJ,QAAQ,GAAGD,MAGzF3Y,IAAW6P,GAAcF,KAAOhV,EAAQgB,YAAchB,EAAQoB,iBAC7D6F,EAAOuO,EAAIR,EAAEiJ,QAAQ,GAAG5C,MACxBpU,EAAOM,EAAIyN,EAAEiJ,QAAQ,GAAGD,OAGrB/W,EAOX,SAAS+G,GAAsBqG,EAAa6J,GACxClW,GAAmB,EAAG,iBAEI,IAAhBkW,IAENxY,IAAa,GAGjB4M,GAAgB+B,EAAYrH,QAAQnO,GAAqBwV,QAE/B,IAAhB6J,IACNxY,IAAa,GAGjBsC,GAAkBX,GAAUlG,eAAgB,YAMhD,SAASsI,GAAa6G,GAGlB,IAAI6N,EAAa3hB,EAAK+b,MAAMjI,GAExBtQ,EAAQkB,MAAQlB,EAAQoB,gBAAkBpB,EAAQgB,UAElDwX,GADkB,qBAAuB2F,EAAa,YACtB,GAE5Bne,EAAQoB,gBAAkBpB,EAAQgB,UACtCuE,GAAUuE,IAAI,OAAQqU,GAGtBhe,EAAUgL,UAAUgT,GAO5B,SAAShC,GAAc7D,GACnB,OACI8F,oBAAqB9F,EACrB+F,iBAAkB/F,EAClBgG,gBAAgBhG,EAChBtM,UAAasM,GAQrB,SAASjH,GAAmBlJ,EAAOiJ,EAAWtN,GAEzB,QAAdsN,EACCtL,GAAgBhC,GAAMsN,GAAajJ,EAKnC7L,EAAEqN,KAAK4U,OAAOC,KAAK1Y,GAAgBhC,IAAQ,SAAS4G,EAAO+T,GACvD3Y,GAAgBhC,GAAM2a,GAAOtW,IA4IzC,SAASkI,GAAiBqO,EAAUvW,EAAOrE,GACvC9D,EAAQ0e,GAAYvW,EACR,aAATrE,IACCuD,GAAUqX,GAAYvW,GAO9B,SAASjI,KAEF5D,EAAE,QAAQ2D,SAAS5C,GAClBshB,GAAU,QAAS,kFAKnB3e,EAAQ4B,qBACP5B,EAAQ0B,SAAW1B,EAAQyB,cAC5BzB,EAAQ4B,oBAAqB,EAC7B+c,GAAU,OAAQ,+GAGnB3e,EAAQgB,WAAahB,EAAQqC,gBAC5Bsc,GAAU,OAAQ,gIAGnB3e,EAAQ4B,qBAAuB5B,EAAQgB,WAAchB,EAAQoB,gBAC5DpB,EAAQ4B,oBAAqB,EAC7B+c,GAAU,OAAQ,4IAGnB3e,EAAQqC,iBAAmBrC,EAAQuC,wBAClCvC,EAAQqC,gBAAiB,EACzBsc,GAAU,QAAS,sHAIvBriB,EAAEqN,MA5BgB,eAAgB,uBAAwB,qBAAsB,oBAAqB,eAAgB,mBAAoB,iBAAkB,cAAe,sBAAuB,YA4B9K,SAASe,EAAOkU,GAE5B5e,EAAQ4e,IACPD,GAAU,OAAQ,6HAA8HC,KAKxJtiB,EAAEqN,KAAK3J,EAAQQ,QAAS,SAASkK,EAAOmS,GAGpC,IAAIgC,EAAWhf,EAAU6J,KAAK,UAAU+C,OAAO,WAC3C,OAAOnQ,EAAEQ,MAAM8N,KAAK,SAAWtO,EAAEQ,MAAM8N,KAAK,QAAQkU,eAAiBjC,EAAKiC,gBAG1EC,EAASlf,EAAU6J,KAAK,QAAQ+C,OAAO,WACvC,OAAOnQ,EAAEQ,MAAM8N,KAAK,OAAStO,EAAEQ,MAAM8N,KAAK,MAAMkU,eAAiBjC,EAAKiC,iBAGvEC,EAAOnX,QAAUiX,EAASjX,UACzB+W,GAAU,QAAS,4GACnBI,EAAOnX,QAAU+W,GAAU,QAAS,IAAM9B,EAAO,uDACjDgC,EAASjX,QAAU+W,GAAU,QAAS,IAAM9B,EAAO,6DAQ/D,SAAS8B,GAAU7a,EAAM8Z,GACrBoB,SAAWA,QAAQlb,IAASkb,QAAQlb,GAAM,aAAe8Z", "file": "jquery.fullpage.min.js", "sourcesContent": ["/*!\r\n * fullPage 2.9.7\r\n * https://github.com/alvarotrigo/fullPage.js\r\n * @license MIT licensed\r\n *\r\n * Copyright (C) 2015 alvarotrigo.com - A project by <PERSON><PERSON>\r\n */\r\n(function(global, factory) {\r\n    'use strict';\r\n    if (typeof define === 'function' && define.amd) {\r\n        define(['jquery'], function($) {\r\n          return factory($, global, global.document, global.Math);\r\n        });\r\n    } else if (typeof exports === \"object\" && exports) {\r\n        module.exports = factory(require('jquery'), global, global.document, global.Math);\r\n    } else {\r\n        factory(jQuery, global, global.document, global.Math);\r\n    }\r\n})(typeof window !== 'undefined' ? window : this, function($, window, document, Math, undefined) {\r\n    'use strict';\r\n\r\n    // keeping central set of classnames and selectors\r\n    var WRAPPER =               'fullpage-wrapper';\r\n    var WRAPPER_SEL =           '.' + WRAPPER;\r\n\r\n    // slimscroll\r\n    var SCROLLABLE =            'fp-scrollable';\r\n    var SCROLLABLE_SEL =        '.' + SCROLLABLE;\r\n\r\n    // util\r\n    var RESPONSIVE =            'fp-responsive';\r\n    var NO_TRANSITION =         'fp-notransition';\r\n    var DESTROYED =             'fp-destroyed';\r\n    var ENABLED =               'fp-enabled';\r\n    var VIEWING_PREFIX =        'fp-viewing';\r\n    var ACTIVE =                'active';\r\n    var ACTIVE_SEL =            '.' + ACTIVE;\r\n    var COMPLETELY =            'fp-completely';\r\n    var COMPLETELY_SEL =        '.' + COMPLETELY;\r\n\r\n    // section\r\n    var SECTION_DEFAULT_SEL =   '.section';\r\n    var SECTION =               'fp-section';\r\n    var SECTION_SEL =           '.' + SECTION;\r\n    var SECTION_ACTIVE_SEL =    SECTION_SEL + ACTIVE_SEL;\r\n    var SECTION_FIRST_SEL =     SECTION_SEL + ':first';\r\n    var SECTION_LAST_SEL =      SECTION_SEL + ':last';\r\n    var TABLE_CELL =            'fp-tableCell';\r\n    var TABLE_CELL_SEL =        '.' + TABLE_CELL;\r\n    var AUTO_HEIGHT =           'fp-auto-height';\r\n    var AUTO_HEIGHT_SEL =       '.fp-auto-height';\r\n    var NORMAL_SCROLL =         'fp-normal-scroll';\r\n    var NORMAL_SCROLL_SEL =     '.fp-normal-scroll';\r\n\r\n    // section nav\r\n    var SECTION_NAV =           'fp-nav';\r\n    var SECTION_NAV_SEL =       '#' + SECTION_NAV;\r\n    var SECTION_NAV_TOOLTIP =   'fp-tooltip';\r\n    var SECTION_NAV_TOOLTIP_SEL='.'+SECTION_NAV_TOOLTIP;\r\n    var SHOW_ACTIVE_TOOLTIP =   'fp-show-active';\r\n\r\n    // slide\r\n    var SLIDE_DEFAULT_SEL =     '.slide';\r\n    var SLIDE =                 'fp-slide';\r\n    var SLIDE_SEL =             '.' + SLIDE;\r\n    var SLIDE_ACTIVE_SEL =      SLIDE_SEL + ACTIVE_SEL;\r\n    var SLIDES_WRAPPER =        'fp-slides';\r\n    var SLIDES_WRAPPER_SEL =    '.' + SLIDES_WRAPPER;\r\n    var SLIDES_CONTAINER =      'fp-slidesContainer';\r\n    var SLIDES_CONTAINER_SEL =  '.' + SLIDES_CONTAINER;\r\n    var TABLE =                 'fp-table';\r\n\r\n    // slide nav\r\n    var SLIDES_NAV =            'fp-slidesNav';\r\n    var SLIDES_NAV_SEL =        '.' + SLIDES_NAV;\r\n    var SLIDES_NAV_LINK_SEL =   SLIDES_NAV_SEL + ' a';\r\n    var SLIDES_ARROW =          'fp-controlArrow';\r\n    var SLIDES_ARROW_SEL =      '.' + SLIDES_ARROW;\r\n    var SLIDES_PREV =           'fp-prev';\r\n    var SLIDES_PREV_SEL =       '.' + SLIDES_PREV;\r\n    var SLIDES_ARROW_PREV =     SLIDES_ARROW + ' ' + SLIDES_PREV;\r\n    var SLIDES_ARROW_PREV_SEL = SLIDES_ARROW_SEL + SLIDES_PREV_SEL;\r\n    var SLIDES_NEXT =           'fp-next';\r\n    var SLIDES_NEXT_SEL =       '.' + SLIDES_NEXT;\r\n    var SLIDES_ARROW_NEXT =     SLIDES_ARROW + ' ' + SLIDES_NEXT;\r\n    var SLIDES_ARROW_NEXT_SEL = SLIDES_ARROW_SEL + SLIDES_NEXT_SEL;\r\n\r\n    var $window = $(window);\r\n    var $document = $(document);\r\n\r\n    $.fn.fullpage = function(options) {\r\n        //only once my friend!\r\n        if($('html').hasClass(ENABLED)){ displayWarnings(); return; }\r\n\r\n        // common jQuery objects\r\n        var $htmlBody = $('html, body');\r\n        var $body = $('body');\r\n\r\n        var FP = $.fn.fullpage;\r\n\r\n        // Creating some defaults, extending them with any options that were provided\r\n        options = $.extend({\r\n            //navigation\r\n            menu: false,\r\n            anchors:[],\r\n            lockAnchors: false,\r\n            navigation: false,\r\n            navigationPosition: 'right',\r\n            navigationTooltips: [],\r\n            showActiveTooltip: false,\r\n            slidesNavigation: false,\r\n            slidesNavPosition: 'bottom',\r\n            scrollBar: false,\r\n            hybrid: false,\r\n\r\n            //scrolling\r\n            css3: true,\r\n            scrollingSpeed: 700,\r\n            autoScrolling: true,\r\n            fitToSection: true,\r\n            fitToSectionDelay: 1000,\r\n            easing: 'easeInOutCubic',\r\n            easingcss3: 'ease',\r\n            loopBottom: false,\r\n            loopTop: false,\r\n            loopHorizontal: true,\r\n            continuousVertical: false,\r\n            continuousHorizontal: false,\r\n            scrollHorizontally: false,\r\n            interlockedSlides: false,\r\n            dragAndMove: false,\r\n            offsetSections: false,\r\n            resetSliders: false,\r\n            fadingEffect: false,\r\n            normalScrollElements: null,\r\n            scrollOverflow: false,\r\n            scrollOverflowReset: false,\r\n            scrollOverflowHandler: $.fn.fp_scrolloverflow ? $.fn.fp_scrolloverflow.iscrollHandler : null,\r\n            scrollOverflowOptions: null,\r\n            touchSensitivity: 5,\r\n            normalScrollElementTouchThreshold: 5,\r\n            bigSectionsDestination: null,\r\n\r\n            //Accessibility\r\n            keyboardScrolling: true,\r\n            animateAnchor: true,\r\n            recordHistory: true,\r\n\r\n            //design\r\n            controlArrows: true,\r\n            controlArrowColor: '#fff',\r\n            verticalCentered: true,\r\n            sectionsColor : [],\r\n            paddingTop: 0,\r\n            paddingBottom: 0,\r\n            fixedElements: null,\r\n            responsive: 0, //backwards compabitility with responsiveWiddth\r\n            responsiveWidth: 0,\r\n            responsiveHeight: 0,\r\n            responsiveSlides: false,\r\n            parallax: false,\r\n            parallaxOptions: {\r\n                type: 'reveal',\r\n                percentage: 62,\r\n                property: 'translate'\r\n            },\r\n\r\n            //Custom selectors\r\n            sectionSelector: SECTION_DEFAULT_SEL,\r\n            slideSelector: SLIDE_DEFAULT_SEL,\r\n\r\n            //events\r\n            afterLoad: null,\r\n            onLeave: null,\r\n            afterRender: null,\r\n            afterResize: null,\r\n            afterReBuild: null,\r\n            afterSlideLoad: null,\r\n            onSlideLeave: null,\r\n            afterResponsive: null,\r\n\r\n            lazyLoading: true\r\n        }, options);\r\n\r\n        //flag to avoid very fast sliding for landscape sliders\r\n        var slideMoving = false;\r\n\r\n        var isTouchDevice = navigator.userAgent.match(/(iPhone|iPod|iPad|Android|playbook|silk|BlackBerry|BB10|Windows Phone|Tizen|Bada|webOS|IEMobile|Opera Mini)/);\r\n        var isTouch = (('ontouchstart' in window) || (navigator.msMaxTouchPoints > 0) || (navigator.maxTouchPoints));\r\n        var container = $(this);\r\n        var windowsHeight = $window.height();\r\n        var isResizing = false;\r\n        var isWindowFocused = true;\r\n        var lastScrolledDestiny;\r\n        var lastScrolledSlide;\r\n        var canScroll = true;\r\n        var scrollings = [];\r\n        var controlPressed;\r\n        var startingSection;\r\n        var isScrollAllowed = {};\r\n        isScrollAllowed.m = {  'up':true, 'down':true, 'left':true, 'right':true };\r\n        isScrollAllowed.k = $.extend(true,{}, isScrollAllowed.m);\r\n        var MSPointer = getMSPointer();\r\n        var events = {\r\n            touchmove: 'ontouchmove' in window ? 'touchmove' :  MSPointer.move,\r\n            touchstart: 'ontouchstart' in window ? 'touchstart' :  MSPointer.down\r\n        };\r\n        var scrollBarHandler;\r\n\r\n        // taken from https://github.com/udacity/ud891/blob/gh-pages/lesson2-focus/07-modals-and-keyboard-traps/solution/modal.js\r\n        var focusableElementsString = 'a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex=\"0\"], [contenteditable]';\r\n\r\n        //timeouts\r\n        var resizeId;\r\n        var afterSectionLoadsId;\r\n        var afterSlideLoadsId;\r\n        var scrollId;\r\n        var scrollId2;\r\n        var keydownId;\r\n        var originals = $.extend(true, {}, options); //deep copy\r\n\r\n        displayWarnings();\r\n\r\n        //easeInOutCubic animation included in the plugin\r\n        $.extend($.easing,{ easeInOutCubic: function (x, t, b, c, d) {if ((t/=d/2) < 1) return c/2*t*t*t + b;return c/2*((t-=2)*t*t + 2) + b;}});\r\n\r\n        /**\r\n        * Sets the autoScroll option.\r\n        * It changes the scroll bar visibility and the history of the site as a result.\r\n        */\r\n        function setAutoScrolling(value, type){\r\n            //removing the transformation\r\n            if(!value){\r\n                silentScroll(0);\r\n            }\r\n\r\n            setVariableState('autoScrolling', value, type);\r\n\r\n            var element = $(SECTION_ACTIVE_SEL);\r\n\r\n            if(options.autoScrolling && !options.scrollBar){\r\n                $htmlBody.css({\r\n                    'overflow' : 'hidden',\r\n                    'height' : '100%'\r\n                });\r\n\r\n                setRecordHistory(originals.recordHistory, 'internal');\r\n\r\n                //for IE touch devices\r\n                container.css({\r\n                    '-ms-touch-action': 'none',\r\n                    'touch-action': 'none'\r\n                });\r\n\r\n                if(element.length){\r\n                    //moving the container up\r\n                    silentScroll(element.position().top);\r\n                }\r\n\r\n            }else{\r\n                $htmlBody.css({\r\n                    'overflow' : 'visible',\r\n                    'height' : 'initial'\r\n                });\r\n\r\n                setRecordHistory(false, 'internal');\r\n\r\n                //for IE touch devices\r\n                container.css({\r\n                    '-ms-touch-action': '',\r\n                    'touch-action': ''\r\n                });\r\n\r\n                //scrolling the page to the section with no animation\r\n                if (element.length) {\r\n                    $htmlBody.scrollTop(element.position().top);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Defines wheter to record the history for each hash change in the URL.\r\n        */\r\n        function setRecordHistory(value, type){\r\n            setVariableState('recordHistory', value, type);\r\n        }\r\n\r\n        /**\r\n        * Defines the scrolling speed\r\n        */\r\n        function setScrollingSpeed(value, type){\r\n            setVariableState('scrollingSpeed', value, type);\r\n        }\r\n\r\n        /**\r\n        * Sets fitToSection\r\n        */\r\n        function setFitToSection(value, type){\r\n            setVariableState('fitToSection', value, type);\r\n        }\r\n\r\n        /**\r\n        * Sets lockAnchors\r\n        */\r\n        function setLockAnchors(value){\r\n            options.lockAnchors = value;\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the possibility of scrolling through sections by using the mouse wheel or the trackpad.\r\n        */\r\n        function setMouseWheelScrolling(value){\r\n            if(value){\r\n                addMouseWheelHandler();\r\n                addMiddleWheelHandler();\r\n            }else{\r\n                removeMouseWheelHandler();\r\n                removeMiddleWheelHandler();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the possibility of scrolling through sections by using the mouse wheel/trackpad or touch gestures.\r\n        * Optionally a second parameter can be used to specify the direction for which the action will be applied.\r\n        *\r\n        * @param directions string containing the direction or directions separated by comma.\r\n        */\r\n        function setAllowScrolling(value, directions){\r\n            if(typeof directions !== 'undefined'){\r\n                directions = directions.replace(/ /g,'').split(',');\r\n\r\n                $.each(directions, function (index, direction){\r\n                    setIsScrollAllowed(value, direction, 'm');\r\n                });\r\n            }\r\n            else{\r\n                setIsScrollAllowed(value, 'all', 'm');\r\n\r\n                if(value){\r\n                    setMouseWheelScrolling(true);\r\n                    addTouchHandler();\r\n                }else{\r\n                    setMouseWheelScrolling(false);\r\n                    removeTouchHandler();\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds or remove the possibility of scrolling through sections by using the keyboard arrow keys\r\n        */\r\n        function setKeyboardScrolling(value, directions){\r\n            if(typeof directions !== 'undefined'){\r\n                directions = directions.replace(/ /g,'').split(',');\r\n\r\n                $.each(directions, function (index, direction){\r\n                    setIsScrollAllowed(value, direction, 'k');\r\n                });\r\n            }else{\r\n                setIsScrollAllowed(value, 'all', 'k');\r\n                options.keyboardScrolling = value;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Moves the page up one section.\r\n        */\r\n        function moveSectionUp(){\r\n            var prev = $(SECTION_ACTIVE_SEL).prev(SECTION_SEL);\r\n\r\n            //looping to the bottom if there's no more sections above\r\n            if (!prev.length && (options.loopTop || options.continuousVertical)) {\r\n                prev = $(SECTION_SEL).last();\r\n            }\r\n\r\n            if (prev.length) {\r\n                scrollPage(prev, null, true);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Moves the page down one section.\r\n        */\r\n        function moveSectionDown(){\r\n            var next = $(SECTION_ACTIVE_SEL).next(SECTION_SEL);\r\n\r\n            //looping to the top if there's no more sections below\r\n            if(!next.length &&\r\n                (options.loopBottom || options.continuousVertical)){\r\n                next = $(SECTION_SEL).first();\r\n            }\r\n\r\n            if(next.length){\r\n                scrollPage(next, null, false);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Moves the page to the given section and slide with no animation.\r\n        * Anchors or index positions can be used as params.\r\n        */\r\n        function silentMoveTo(sectionAnchor, slideAnchor){\r\n            setScrollingSpeed (0, 'internal');\r\n            moveTo(sectionAnchor, slideAnchor);\r\n            setScrollingSpeed (originals.scrollingSpeed, 'internal');\r\n        }\r\n\r\n        /**\r\n        * Moves the page to the given section and slide.\r\n        * Anchors or index positions can be used as params.\r\n        */\r\n        function moveTo(sectionAnchor, slideAnchor){\r\n            var destiny = getSectionByAnchor(sectionAnchor);\r\n\r\n            if (typeof slideAnchor !== 'undefined'){\r\n                scrollPageAndSlide(sectionAnchor, slideAnchor);\r\n            }else if(destiny.length > 0){\r\n                scrollPage(destiny);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Slides right the slider of the active section.\r\n        * Optional `section` param.\r\n        */\r\n        function moveSlideRight(section){\r\n            moveSlide('right', section);\r\n        }\r\n\r\n        /**\r\n        * Slides left the slider of the active section.\r\n        * Optional `section` param.\r\n        */\r\n        function moveSlideLeft(section){\r\n            moveSlide('left', section);\r\n        }\r\n\r\n        /**\r\n         * When resizing is finished, we adjust the slides sizes and positions\r\n         */\r\n        function reBuild(resizing){\r\n            if(container.hasClass(DESTROYED)){ return; }  //nothing to do if the plugin was destroyed\r\n\r\n            isResizing = true;\r\n\r\n            windowsHeight = $window.height();  //updating global var\r\n\r\n            $(SECTION_SEL).each(function(){\r\n                var slidesWrap = $(this).find(SLIDES_WRAPPER_SEL);\r\n                var slides = $(this).find(SLIDE_SEL);\r\n\r\n                //adjusting the height of the table-cell for IE and Firefox\r\n                if(options.verticalCentered){\r\n                    $(this).find(TABLE_CELL_SEL).css('height', getTableHeight($(this)) + 'px');\r\n                }\r\n\r\n                $(this).css('height', windowsHeight + 'px');\r\n\r\n                //adjusting the position fo the FULL WIDTH slides...\r\n                if (slides.length > 1) {\r\n                    landscapeScroll(slidesWrap, slidesWrap.find(SLIDE_ACTIVE_SEL));\r\n                }\r\n            });\r\n\r\n            if(options.scrollOverflow){\r\n                scrollBarHandler.createScrollBarForAll();\r\n            }\r\n\r\n            var activeSection = $(SECTION_ACTIVE_SEL);\r\n            var sectionIndex = activeSection.index(SECTION_SEL);\r\n\r\n            //isn't it the first section?\r\n            if(sectionIndex){\r\n                //adjusting the position for the current section\r\n                silentMoveTo(sectionIndex + 1);\r\n            }\r\n\r\n            isResizing = false;\r\n            $.isFunction( options.afterResize ) && resizing && options.afterResize.call(container);\r\n            $.isFunction( options.afterReBuild ) && !resizing && options.afterReBuild.call(container);\r\n        }\r\n\r\n        /**\r\n        * Turns fullPage.js to normal scrolling mode when the viewport `width` or `height`\r\n        * are smaller than the set limit values.\r\n        */\r\n        function setResponsive(active){\r\n            var isResponsive = $body.hasClass(RESPONSIVE);\r\n\r\n            if(active){\r\n                if(!isResponsive){\r\n                    setAutoScrolling(false, 'internal');\r\n                    setFitToSection(false, 'internal');\r\n                    $(SECTION_NAV_SEL).hide();\r\n                    $body.addClass(RESPONSIVE);\r\n                    $.isFunction( options.afterResponsive ) && options.afterResponsive.call( container, active);\r\n                }\r\n            }\r\n            else if(isResponsive){\r\n                setAutoScrolling(originals.autoScrolling, 'internal');\r\n                setFitToSection(originals.autoScrolling, 'internal');\r\n                $(SECTION_NAV_SEL).show();\r\n                $body.removeClass(RESPONSIVE);\r\n                $.isFunction( options.afterResponsive ) && options.afterResponsive.call( container, active);\r\n            }\r\n        }\r\n\r\n        if($(this).length){\r\n            //public functions\r\n            FP.version = '2.9.6';\r\n            FP.setAutoScrolling = setAutoScrolling;\r\n            FP.setRecordHistory = setRecordHistory;\r\n            FP.setScrollingSpeed = setScrollingSpeed;\r\n            FP.setFitToSection = setFitToSection;\r\n            FP.setLockAnchors = setLockAnchors;\r\n            FP.setMouseWheelScrolling = setMouseWheelScrolling;\r\n            FP.setAllowScrolling = setAllowScrolling;\r\n            FP.setKeyboardScrolling = setKeyboardScrolling;\r\n            FP.moveSectionUp = moveSectionUp;\r\n            FP.moveSectionDown = moveSectionDown;\r\n            FP.silentMoveTo = silentMoveTo;\r\n            FP.moveTo = moveTo;\r\n            FP.moveSlideRight = moveSlideRight;\r\n            FP.moveSlideLeft = moveSlideLeft;\r\n            FP.fitToSection = fitToSection;\r\n            FP.reBuild = reBuild;\r\n            FP.setResponsive = setResponsive;\r\n            FP.destroy = destroy;\r\n\r\n            //functions we want to share across files but which are not\r\n            //mean to be used on their own by developers\r\n            FP.shared ={\r\n                afterRenderActions: afterRenderActions\r\n            };\r\n\r\n            init();\r\n\r\n            bindEvents();\r\n        }\r\n\r\n        function init(){\r\n            //if css3 is not supported, it will use jQuery animations\r\n            if(options.css3){\r\n                options.css3 = support3d();\r\n            }\r\n\r\n            options.scrollBar = options.scrollBar || options.hybrid;\r\n\r\n            setOptionsFromDOM();\r\n            prepareDom();\r\n            setAllowScrolling(true);\r\n            setAutoScrolling(options.autoScrolling, 'internal');\r\n            responsive();\r\n\r\n            //setting the class for the body element\r\n            setBodyClass();\r\n\r\n            if(document.readyState === 'complete'){\r\n                scrollToAnchor();\r\n            }\r\n            $window.on('load', scrollToAnchor);\r\n        }\r\n\r\n        function bindEvents(){\r\n            $window\r\n                //when scrolling...\r\n                .on('scroll', scrollHandler)\r\n\r\n                //detecting any change on the URL to scroll to the given anchor link\r\n                //(a way to detect back history button as we play with the hashes on the URL)\r\n                .on('hashchange', hashChangeHandler)\r\n\r\n                //when opening a new tab (ctrl + t), `control` won't be pressed when coming back.\r\n                .blur(blurHandler)\r\n\r\n                //when resizing the site, we adjust the heights of the sections, slimScroll...\r\n                .resize(resizeHandler);\r\n\r\n            $document\r\n                //Sliding with arrow keys, both, vertical and horizontal\r\n                .keydown(keydownHandler)\r\n\r\n                //to prevent scrolling while zooming\r\n                .keyup(keyUpHandler)\r\n\r\n                //Scrolls to the section when clicking the navigation bullet\r\n                .on('click touchstart', SECTION_NAV_SEL + ' a', sectionBulletHandler)\r\n\r\n                //Scrolls the slider to the given slide destination for the given section\r\n                .on('click touchstart', SLIDES_NAV_LINK_SEL, slideBulletHandler)\r\n\r\n                .on('click', SECTION_NAV_TOOLTIP_SEL, tooltipTextHandler);\r\n\r\n            //Scrolling horizontally when clicking on the slider controls.\r\n            $(SECTION_SEL).on('click touchstart', SLIDES_ARROW_SEL, slideArrowHandler);\r\n\r\n            /**\r\n            * Applying normalScroll elements.\r\n            * Ignoring the scrolls over the specified selectors.\r\n            */\r\n            if(options.normalScrollElements){\r\n                $document.on('mouseenter touchstart', options.normalScrollElements, function () {\r\n                    setAllowScrolling(false);\r\n                });\r\n\r\n                $document.on('mouseleave touchend', options.normalScrollElements, function(){\r\n                    setAllowScrolling(true);\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Setting options from DOM elements if they are not provided.\r\n        */\r\n        function setOptionsFromDOM(){\r\n            var sections = container.find(options.sectionSelector);\r\n\r\n            //no anchors option? Checking for them in the DOM attributes\r\n            if(!options.anchors.length){\r\n                options.anchors = sections.filter('[data-anchor]').map(function(){\r\n                    return $(this).data('anchor').toString();\r\n                }).get();\r\n            }\r\n\r\n            //no tooltips option? Checking for them in the DOM attributes\r\n            if(!options.navigationTooltips.length){\r\n                options.navigationTooltips = sections.filter('[data-tooltip]').map(function(){\r\n                    return $(this).data('tooltip').toString();\r\n                }).get();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Works over the DOM structure to set it up for the current fullpage options.\r\n        */\r\n        function prepareDom(){\r\n            container.css({\r\n                'height': '100%',\r\n                'position': 'relative'\r\n            });\r\n\r\n            //adding a class to recognize the container internally in the code\r\n            container.addClass(WRAPPER);\r\n            $('html').addClass(ENABLED);\r\n\r\n            //due to https://github.com/alvarotrigo/fullPage.js/issues/1502\r\n            windowsHeight = $window.height();\r\n\r\n            container.removeClass(DESTROYED); //in case it was destroyed before initializing it again\r\n\r\n            addInternalSelectors();\r\n\r\n            //styling the sections / slides / menu\r\n            $(SECTION_SEL).each(function(index){\r\n                var section = $(this);\r\n                var slides = section.find(SLIDE_SEL);\r\n                var numSlides = slides.length;\r\n\r\n                //caching the original styles to add them back on destroy('all')\r\n                section.data('fp-styles', section.attr('style'));\r\n\r\n                styleSection(section, index);\r\n                styleMenu(section, index);\r\n\r\n                // if there's any slide\r\n                if (numSlides > 0) {\r\n                    styleSlides(section, slides, numSlides);\r\n                }else{\r\n                    if(options.verticalCentered){\r\n                        addTableClass(section);\r\n                    }\r\n                }\r\n            });\r\n\r\n            //fixed elements need to be moved out of the plugin container due to problems with CSS3.\r\n            if(options.fixedElements && options.css3){\r\n                $(options.fixedElements).appendTo($body);\r\n            }\r\n\r\n            //vertical centered of the navigation + active bullet\r\n            if(options.navigation){\r\n                addVerticalNavigation();\r\n            }\r\n\r\n            enableYoutubeAPI();\r\n\r\n            if(options.scrollOverflow){\r\n                scrollBarHandler = options.scrollOverflowHandler.init(options);\r\n            }else{\r\n                afterRenderActions();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Styles the horizontal slides for a section.\r\n        */\r\n        function styleSlides(section, slides, numSlides){\r\n            var sliderWidth = numSlides * 100;\r\n            var slideWidth = 100 / numSlides;\r\n\r\n            slides.wrapAll('<div class=\"' + SLIDES_CONTAINER + '\" />');\r\n            slides.parent().wrap('<div class=\"' + SLIDES_WRAPPER + '\" />');\r\n\r\n            section.find(SLIDES_CONTAINER_SEL).css('width', sliderWidth + '%');\r\n\r\n            if(numSlides > 1){\r\n                if(options.controlArrows){\r\n                    createSlideArrows(section);\r\n                }\r\n\r\n                if(options.slidesNavigation){\r\n                    addSlidesNavigation(section, numSlides);\r\n                }\r\n            }\r\n\r\n            slides.each(function(index) {\r\n                $(this).css('width', slideWidth + '%');\r\n\r\n                if(options.verticalCentered){\r\n                    addTableClass($(this));\r\n                }\r\n            });\r\n\r\n            var startingSlide = section.find(SLIDE_ACTIVE_SEL);\r\n\r\n            //if the slide won't be an starting point, the default will be the first one\r\n            //the active section isn't the first one? Is not the first slide of the first section? Then we load that section/slide by default.\r\n            if( startingSlide.length &&  ($(SECTION_ACTIVE_SEL).index(SECTION_SEL) !== 0 || ($(SECTION_ACTIVE_SEL).index(SECTION_SEL) === 0 && startingSlide.index() !== 0))){\r\n                silentLandscapeScroll(startingSlide, 'internal');\r\n            }else{\r\n                slides.eq(0).addClass(ACTIVE);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Styling vertical sections\r\n        */\r\n        function styleSection(section, index){\r\n            //if no active section is defined, the 1st one will be the default one\r\n            if(!index && $(SECTION_ACTIVE_SEL).length === 0) {\r\n                section.addClass(ACTIVE);\r\n            }\r\n            startingSection = $(SECTION_ACTIVE_SEL);\r\n\r\n            section.css('height', windowsHeight + 'px');\r\n\r\n            if(options.paddingTop){\r\n                section.css('padding-top', options.paddingTop);\r\n            }\r\n\r\n            if(options.paddingBottom){\r\n                section.css('padding-bottom', options.paddingBottom);\r\n            }\r\n\r\n            if (typeof options.sectionsColor[index] !==  'undefined') {\r\n                section.css('background-color', options.sectionsColor[index]);\r\n            }\r\n\r\n            if (typeof options.anchors[index] !== 'undefined') {\r\n                section.attr('data-anchor', options.anchors[index]);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets the data-anchor attributes to the menu elements and activates the current one.\r\n        */\r\n        function styleMenu(section, index){\r\n            if (typeof options.anchors[index] !== 'undefined') {\r\n                //activating the menu / nav element on load\r\n                if(section.hasClass(ACTIVE)){\r\n                    activateMenuAndNav(options.anchors[index], index);\r\n                }\r\n            }\r\n\r\n            //moving the menu outside the main container if it is inside (avoid problems with fixed positions when using CSS3 tranforms)\r\n            if(options.menu && options.css3 && $(options.menu).closest(WRAPPER_SEL).length){\r\n                $(options.menu).appendTo($body);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds internal classes to be able to provide customizable selectors\r\n        * keeping the link with the style sheet.\r\n        */\r\n        function addInternalSelectors(){\r\n            container.find(options.sectionSelector).addClass(SECTION);\r\n            container.find(options.slideSelector).addClass(SLIDE);\r\n        }\r\n\r\n        /**\r\n        * Creates the control arrows for the given section\r\n        */\r\n        function createSlideArrows(section){\r\n            section.find(SLIDES_WRAPPER_SEL).after('<div class=\"' + SLIDES_ARROW_PREV + '\"></div><div class=\"' + SLIDES_ARROW_NEXT + '\"></div>');\r\n\r\n            if(options.controlArrowColor!='#fff'){\r\n                section.find(SLIDES_ARROW_NEXT_SEL).css('border-color', 'transparent transparent transparent '+options.controlArrowColor);\r\n                section.find(SLIDES_ARROW_PREV_SEL).css('border-color', 'transparent '+ options.controlArrowColor + ' transparent transparent');\r\n            }\r\n\r\n            if(!options.loopHorizontal){\r\n                section.find(SLIDES_ARROW_PREV_SEL).hide();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Creates a vertical navigation bar.\r\n        */\r\n        function addVerticalNavigation(){\r\n            $body.append('<div id=\"' + SECTION_NAV + '\"><ul></ul></div>');\r\n            var nav = $(SECTION_NAV_SEL);\r\n\r\n            nav.addClass(function() {\r\n                return options.showActiveTooltip ? SHOW_ACTIVE_TOOLTIP + ' ' + options.navigationPosition : options.navigationPosition;\r\n            });\r\n\r\n            for (var i = 0; i < $(SECTION_SEL).length; i++) {\r\n                var link = '';\r\n                if (options.anchors.length) {\r\n                    link = options.anchors[i];\r\n                }\r\n\r\n                var li = '<li><a href=\"#' + link + '\"><span></span></a>';\r\n\r\n                // Only add tooltip if needed (defined by user)\r\n                var tooltip = options.navigationTooltips[i];\r\n\r\n                if (typeof tooltip !== 'undefined' && tooltip !== '') {\r\n                    li += '<div class=\"' + SECTION_NAV_TOOLTIP + ' ' + options.navigationPosition + '\">' + tooltip + '</div>';\r\n                }\r\n\r\n                li += '</li>';\r\n\r\n                nav.find('ul').append(li);\r\n            }\r\n\r\n            //centering it vertically\r\n            $(SECTION_NAV_SEL).css('margin-top', '-' + ($(SECTION_NAV_SEL).height()/2) + 'px');\r\n\r\n            //activating the current active section\r\n            $(SECTION_NAV_SEL).find('li').eq($(SECTION_ACTIVE_SEL).index(SECTION_SEL)).find('a').addClass(ACTIVE);\r\n        }\r\n\r\n        /*\r\n        * Enables the Youtube videos API so we can control their flow if necessary.\r\n        */\r\n        function enableYoutubeAPI(){\r\n            container.find('iframe[src*=\"youtube.com/embed/\"]').each(function(){\r\n                addURLParam($(this), 'enablejsapi=1');\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Adds a new parameter and its value to the `src` of a given element\r\n        */\r\n        function addURLParam(element, newParam){\r\n            var originalSrc = element.attr('src');\r\n            element.attr('src', originalSrc + getUrlParamSign(originalSrc) + newParam);\r\n        }\r\n\r\n        /*\r\n        * Returns the prefix sign to use for a new parameter in an existen URL.\r\n        *\r\n        * @return {String}  ? | &\r\n        */\r\n        function getUrlParamSign(url){\r\n            return ( !/\\?/.test( url ) ) ? '?' : '&';\r\n        }\r\n\r\n        /**\r\n        * Actions and callbacks to fire afterRender\r\n        */\r\n        function afterRenderActions(){\r\n            var section = $(SECTION_ACTIVE_SEL);\r\n\r\n            section.addClass(COMPLETELY);\r\n\r\n            lazyLoad(section);\r\n            playMedia(section);\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.afterLoad();\r\n            }\r\n\r\n            if(isDestinyTheStartingSection()){\r\n                $.isFunction( options.afterLoad ) && options.afterLoad.call(section, section.data('anchor'), (section.index(SECTION_SEL) + 1));\r\n            }\r\n\r\n            $.isFunction( options.afterRender ) && options.afterRender.call(container);\r\n        }\r\n\r\n        /**\r\n        * Determines if the URL anchor destiny is the starting section (the one using 'active' class before initialization)\r\n        */\r\n        function isDestinyTheStartingSection(){\r\n            var destinationSection = getSectionByAnchor(getAnchorsURL().section);\r\n            return !destinationSection || destinationSection.length && destinationSection.index() === startingSection.index();\r\n        }\r\n\r\n\r\n        var isScrolling = false;\r\n        var lastScroll = 0;\r\n\r\n        //when scrolling...\r\n        function scrollHandler(){\r\n            var currentSection;\r\n\r\n            if(!options.autoScrolling || options.scrollBar){\r\n                var currentScroll = $window.scrollTop();\r\n                var scrollDirection = getScrollDirection(currentScroll);\r\n                var visibleSectionIndex = 0;\r\n                var screen_mid = currentScroll + ($window.height() / 2.0);\r\n                var isAtBottom = $body.height() - $window.height() === currentScroll;\r\n                var sections =  document.querySelectorAll(SECTION_SEL);\r\n\r\n                //when using `auto-height` for a small last section it won't be centered in the viewport\r\n                if(isAtBottom){\r\n                    visibleSectionIndex = sections.length - 1;\r\n                }\r\n                //is at top? when using `auto-height` for a small first section it won't be centered in the viewport\r\n                else if(!currentScroll){\r\n                    visibleSectionIndex = 0;\r\n                }\r\n\r\n                //taking the section which is showing more content in the viewport\r\n                else{\r\n                    for (var i = 0; i < sections.length; ++i) {\r\n                        var section = sections[i];\r\n\r\n                        // Pick the the last section which passes the middle line of the screen.\r\n                        if (section.offsetTop <= screen_mid)\r\n                        {\r\n                            visibleSectionIndex = i;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if(isCompletelyInViewPort(scrollDirection)){\r\n                    if(!$(SECTION_ACTIVE_SEL).hasClass(COMPLETELY)){\r\n                        $(SECTION_ACTIVE_SEL).addClass(COMPLETELY).siblings().removeClass(COMPLETELY);\r\n                    }\r\n                }\r\n\r\n                //geting the last one, the current one on the screen\r\n                currentSection = $(sections).eq(visibleSectionIndex);\r\n\r\n                //setting the visible section as active when manually scrolling\r\n                //executing only once the first time we reach the section\r\n                if(!currentSection.hasClass(ACTIVE)){\r\n                    isScrolling = true;\r\n                    var leavingSection = $(SECTION_ACTIVE_SEL);\r\n                    var leavingSectionIndex = leavingSection.index(SECTION_SEL) + 1;\r\n                    var yMovement = getYmovement(currentSection);\r\n                    var anchorLink  = currentSection.data('anchor');\r\n                    var sectionIndex = currentSection.index(SECTION_SEL) + 1;\r\n                    var activeSlide = currentSection.find(SLIDE_ACTIVE_SEL);\r\n                    var slideIndex;\r\n                    var slideAnchorLink;\r\n\r\n                    if(activeSlide.length){\r\n                        slideAnchorLink = activeSlide.data('anchor');\r\n                        slideIndex = activeSlide.index();\r\n                    }\r\n\r\n                    if(canScroll){\r\n                        currentSection.addClass(ACTIVE).siblings().removeClass(ACTIVE);\r\n\r\n                        $.isFunction( options.onLeave ) && options.onLeave.call( leavingSection, leavingSectionIndex, sectionIndex, yMovement);\r\n                        $.isFunction( options.afterLoad ) && options.afterLoad.call( currentSection, anchorLink, sectionIndex);\r\n\r\n                        stopMedia(leavingSection);\r\n                        lazyLoad(currentSection);\r\n                        playMedia(currentSection);\r\n\r\n                        activateMenuAndNav(anchorLink, sectionIndex - 1);\r\n\r\n                        if(options.anchors.length){\r\n                            //needed to enter in hashChange event when using the menu with anchor links\r\n                            lastScrolledDestiny = anchorLink;\r\n                        }\r\n                        setState(slideIndex, slideAnchorLink, anchorLink, sectionIndex);\r\n                    }\r\n\r\n                    //small timeout in order to avoid entering in hashChange event when scrolling is not finished yet\r\n                    clearTimeout(scrollId);\r\n                    scrollId = setTimeout(function(){\r\n                        isScrolling = false;\r\n                    }, 100);\r\n                }\r\n\r\n                if(options.fitToSection){\r\n                    //for the auto adjust of the viewport to fit a whole section\r\n                    clearTimeout(scrollId2);\r\n\r\n                    scrollId2 = setTimeout(function(){\r\n                        //checking it again in case it changed during the delay\r\n                        if(options.fitToSection &&\r\n\r\n                            //is the destination element bigger than the viewport?\r\n                            $(SECTION_ACTIVE_SEL).outerHeight() <= windowsHeight\r\n                        ){\r\n                            fitToSection();\r\n                        }\r\n                    }, options.fitToSectionDelay);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Fits the site to the nearest active section\r\n        */\r\n        function fitToSection(){\r\n            //checking fitToSection again in case it was set to false before the timeout delay\r\n            if(canScroll){\r\n                //allows to scroll to an active section and\r\n                //if the section is already active, we prevent firing callbacks\r\n                isResizing = true;\r\n\r\n                scrollPage($(SECTION_ACTIVE_SEL));\r\n                isResizing = false;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Determines whether the active section has seen in its whole or not.\r\n        */\r\n        function isCompletelyInViewPort(movement){\r\n            var top = $(SECTION_ACTIVE_SEL).position().top;\r\n            var bottom = top + $window.height();\r\n\r\n            if(movement == 'up'){\r\n                return bottom >= ($window.scrollTop() + $window.height());\r\n            }\r\n            return top <= $window.scrollTop();\r\n        }\r\n\r\n        /**\r\n        * Gets the directon of the the scrolling fired by the scroll event.\r\n        */\r\n        function getScrollDirection(currentScroll){\r\n            var direction = currentScroll > lastScroll ? 'down' : 'up';\r\n\r\n            lastScroll = currentScroll;\r\n\r\n            //needed for auto-height sections to determine if we want to scroll to the top or bottom of the destination\r\n            previousDestTop = currentScroll;\r\n\r\n            return direction;\r\n        }\r\n\r\n        /**\r\n        * Determines the way of scrolling up or down:\r\n        * by 'automatically' scrolling a section or by using the default and normal scrolling.\r\n        */\r\n        function scrolling(type){\r\n            if (!isScrollAllowed.m[type]){\r\n                return;\r\n            }\r\n\r\n            var scrollSection = (type === 'down') ? moveSectionDown : moveSectionUp;\r\n\r\n            if(options.scrollOverflow){\r\n                var scrollable = options.scrollOverflowHandler.scrollable($(SECTION_ACTIVE_SEL));\r\n                var check = (type === 'down') ? 'bottom' : 'top';\r\n\r\n                if(scrollable.length > 0 ){\r\n                    //is the scrollbar at the start/end of the scroll?\r\n                    if(options.scrollOverflowHandler.isScrolled(check, scrollable)){\r\n                        scrollSection();\r\n                    }else{\r\n                        return true;\r\n                    }\r\n                }else{\r\n                    // moved up/down\r\n                    scrollSection();\r\n                }\r\n            }else{\r\n                // moved up/down\r\n                scrollSection();\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Preventing bouncing in iOS #2285\r\n        */\r\n        function preventBouncing(event){\r\n            var e = event.originalEvent;\r\n            if(options.autoScrolling && isReallyTouch(e)){\r\n                //preventing the easing on iOS devices\r\n                event.preventDefault();\r\n            }\r\n        }\r\n\r\n        var touchStartY = 0;\r\n        var touchStartX = 0;\r\n        var touchEndY = 0;\r\n        var touchEndX = 0;\r\n\r\n        /* Detecting touch events\r\n\r\n        * As we are changing the top property of the page on scrolling, we can not use the traditional way to detect it.\r\n        * This way, the touchstart and the touch moves shows an small difference between them which is the\r\n        * used one to determine the direction.\r\n        */\r\n        function touchMoveHandler(event){\r\n            var e = event.originalEvent;\r\n            var activeSection = $(e.target).closest(SECTION_SEL);\r\n\r\n            // additional: if one of the normalScrollElements isn't within options.normalScrollElementTouchThreshold hops up the DOM chain\r\n            if (isReallyTouch(e) ) {\r\n\r\n                if(options.autoScrolling){\r\n                    //preventing the easing on iOS devices\r\n                    event.preventDefault();\r\n                }\r\n\r\n                var touchEvents = getEventsPage(e);\r\n\r\n                touchEndY = touchEvents.y;\r\n                touchEndX = touchEvents.x;\r\n\r\n                //if movement in the X axys is greater than in the Y and the currect section has slides...\r\n                if (activeSection.find(SLIDES_WRAPPER_SEL).length && Math.abs(touchStartX - touchEndX) > (Math.abs(touchStartY - touchEndY))) {\r\n\r\n                    //is the movement greater than the minimum resistance to scroll?\r\n                    if (!slideMoving && Math.abs(touchStartX - touchEndX) > ($window.outerWidth() / 100 * options.touchSensitivity)) {\r\n                        if (touchStartX > touchEndX) {\r\n                            if(isScrollAllowed.m.right){\r\n                                moveSlideRight(activeSection); //next\r\n                            }\r\n                        } else {\r\n                            if(isScrollAllowed.m.left){\r\n                                moveSlideLeft(activeSection); //prev\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                //vertical scrolling (only when autoScrolling is enabled)\r\n                else if(options.autoScrolling && canScroll){\r\n\r\n                    //is the movement greater than the minimum resistance to scroll?\r\n                    if (Math.abs(touchStartY - touchEndY) > ($window.height() / 100 * options.touchSensitivity)) {\r\n                        if (touchStartY > touchEndY) {\r\n                            scrolling('down');\r\n                        } else if (touchEndY > touchStartY) {\r\n                            scrolling('up');\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * As IE >= 10 fires both touch and mouse events when using a mouse in a touchscreen\r\n        * this way we make sure that is really a touch event what IE is detecting.\r\n        */\r\n        function isReallyTouch(e){\r\n            //if is not IE   ||  IE is detecting `touch` or `pen`\r\n            return typeof e.pointerType === 'undefined' || e.pointerType != 'mouse';\r\n        }\r\n\r\n        /**\r\n        * Handler for the touch start event.\r\n        */\r\n        function touchStartHandler(event){\r\n            var e = event.originalEvent;\r\n\r\n            //stopping the auto scroll to adjust to a section\r\n            if(options.fitToSection){\r\n                $htmlBody.stop();\r\n            }\r\n\r\n            if(isReallyTouch(e)){\r\n                var touchEvents = getEventsPage(e);\r\n                touchStartY = touchEvents.y;\r\n                touchStartX = touchEvents.x;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the average of the last `number` elements of the given array.\r\n        */\r\n        function getAverage(elements, number){\r\n            var sum = 0;\r\n\r\n            //taking `number` elements from the end to make the average, if there are not enought, 1\r\n            var lastElements = elements.slice(Math.max(elements.length - number, 1));\r\n\r\n            for(var i = 0; i < lastElements.length; i++){\r\n                sum = sum + lastElements[i];\r\n            }\r\n\r\n            return Math.ceil(sum/number);\r\n        }\r\n\r\n        /**\r\n         * Detecting mousewheel scrolling\r\n         *\r\n         * http://blogs.sitepointstatic.com/examples/tech/mouse-wheel/index.html\r\n         * http://www.sitepoint.com/html5-javascript-mouse-wheel/\r\n         */\r\n        var prevTime = new Date().getTime();\r\n\r\n        function MouseWheelHandler(e) {\r\n            var curTime = new Date().getTime();\r\n            var isNormalScroll = $(COMPLETELY_SEL).hasClass(NORMAL_SCROLL);\r\n\r\n            //autoscrolling and not zooming?\r\n            if(options.autoScrolling && !controlPressed && !isNormalScroll){\r\n                // cross-browser wheel delta\r\n                e = e || window.event;\r\n                var value = e.wheelDelta || -e.deltaY || -e.detail;\r\n                var delta = Math.max(-1, Math.min(1, value));\r\n\r\n                var horizontalDetection = typeof e.wheelDeltaX !== 'undefined' || typeof e.deltaX !== 'undefined';\r\n                var isScrollingVertically = (Math.abs(e.wheelDeltaX) < Math.abs(e.wheelDelta)) || (Math.abs(e.deltaX ) < Math.abs(e.deltaY) || !horizontalDetection);\r\n\r\n                //Limiting the array to 150 (lets not waste memory!)\r\n                if(scrollings.length > 149){\r\n                    scrollings.shift();\r\n                }\r\n\r\n                //keeping record of the previous scrollings\r\n                scrollings.push(Math.abs(value));\r\n\r\n                //preventing to scroll the site on mouse wheel when scrollbar is present\r\n                if(options.scrollBar){\r\n                    e.preventDefault ? e.preventDefault() : e.returnValue = false;\r\n                }\r\n\r\n                //time difference between the last scroll and the current one\r\n                var timeDiff = curTime-prevTime;\r\n                prevTime = curTime;\r\n\r\n                //haven't they scrolled in a while?\r\n                //(enough to be consider a different scrolling action to scroll another section)\r\n                if(timeDiff > 200){\r\n                    //emptying the array, we dont care about old scrollings for our averages\r\n                    scrollings = [];\r\n                }\r\n\r\n                if(canScroll){\r\n                    var averageEnd = getAverage(scrollings, 10);\r\n                    var averageMiddle = getAverage(scrollings, 70);\r\n                    var isAccelerating = averageEnd >= averageMiddle;\r\n\r\n                    //to avoid double swipes...\r\n                    if(isAccelerating && isScrollingVertically){\r\n                        //scrolling down?\r\n                        if (delta < 0) {\r\n                            scrolling('down');\r\n\r\n                        //scrolling up?\r\n                        }else {\r\n                            scrolling('up');\r\n                        }\r\n                    }\r\n                }\r\n\r\n                return false;\r\n            }\r\n\r\n            if(options.fitToSection){\r\n                //stopping the auto scroll to adjust to a section\r\n                $htmlBody.stop();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Slides a slider to the given direction.\r\n        * Optional `section` param.\r\n        */\r\n        function moveSlide(direction, section){\r\n            var activeSection = typeof section === 'undefined' ? $(SECTION_ACTIVE_SEL) : section;\r\n            var slides = activeSection.find(SLIDES_WRAPPER_SEL);\r\n            var numSlides = slides.find(SLIDE_SEL).length;\r\n\r\n            // more than one slide needed and nothing should be sliding\r\n            if (!slides.length || slideMoving || numSlides < 2) {\r\n                return;\r\n            }\r\n\r\n            var currentSlide = slides.find(SLIDE_ACTIVE_SEL);\r\n            var destiny = null;\r\n\r\n            if(direction === 'left'){\r\n                destiny = currentSlide.prev(SLIDE_SEL);\r\n            }else{\r\n                destiny = currentSlide.next(SLIDE_SEL);\r\n            }\r\n\r\n            //isn't there a next slide in the secuence?\r\n            if(!destiny.length){\r\n                //respect loopHorizontal settin\r\n                if (!options.loopHorizontal) return;\r\n\r\n                if(direction === 'left'){\r\n                    destiny = currentSlide.siblings(':last');\r\n                }else{\r\n                    destiny = currentSlide.siblings(':first');\r\n                }\r\n            }\r\n\r\n            slideMoving = true;\r\n\r\n            landscapeScroll(slides, destiny, direction);\r\n        }\r\n\r\n        /**\r\n        * Maintains the active slides in the viewport\r\n        * (Because the `scroll` animation might get lost with some actions, such as when using continuousVertical)\r\n        */\r\n        function keepSlidesPosition(){\r\n            $(SLIDE_ACTIVE_SEL).each(function(){\r\n                silentLandscapeScroll($(this), 'internal');\r\n            });\r\n        }\r\n\r\n        var previousDestTop = 0;\r\n        /**\r\n        * Returns the destination Y position based on the scrolling direction and\r\n        * the height of the section.\r\n        */\r\n        function getDestinationPosition(element){\r\n            var elemPosition = element.position();\r\n\r\n            //top of the desination will be at the top of the viewport\r\n            var position = elemPosition.top;\r\n            var isScrollingDown =  elemPosition.top > previousDestTop;\r\n            var sectionBottom = position - windowsHeight + element.outerHeight();\r\n            var bigSectionsDestination = options.bigSectionsDestination;\r\n\r\n            //is the destination element bigger than the viewport?\r\n            if(element.outerHeight() > windowsHeight){\r\n                //scrolling up?\r\n                if(!isScrollingDown && !bigSectionsDestination || bigSectionsDestination === 'bottom' ){\r\n                    position = sectionBottom;\r\n                }\r\n            }\r\n\r\n            //sections equal or smaller than the viewport height && scrolling down? ||  is resizing and its in the last section\r\n            else if(isScrollingDown || (isResizing && element.is(':last-child')) ){\r\n                //The bottom of the destination will be at the bottom of the viewport\r\n                position = sectionBottom;\r\n            }\r\n\r\n            /*\r\n            Keeping record of the last scrolled position to determine the scrolling direction.\r\n            No conventional methods can be used as the scroll bar might not be present\r\n            AND the section might not be active if it is auto-height and didnt reach the middle\r\n            of the viewport.\r\n            */\r\n            previousDestTop = position;\r\n            return position;\r\n        }\r\n\r\n        /**\r\n        * Scrolls the site to the given element and scrolls to the slide if a callback is given.\r\n        */\r\n        function scrollPage(element, callback, isMovementUp){\r\n            if(typeof element === 'undefined'){ return; } //there's no element to scroll, leaving the function\r\n\r\n            var dtop = getDestinationPosition(element);\r\n            var slideAnchorLink;\r\n            var slideIndex;\r\n\r\n            //local variables\r\n            var v = {\r\n                element: element,\r\n                callback: callback,\r\n                isMovementUp: isMovementUp,\r\n                dtop: dtop,\r\n                yMovement: getYmovement(element),\r\n                anchorLink: element.data('anchor'),\r\n                sectionIndex: element.index(SECTION_SEL),\r\n                activeSlide: element.find(SLIDE_ACTIVE_SEL),\r\n                activeSection: $(SECTION_ACTIVE_SEL),\r\n                leavingSection: $(SECTION_ACTIVE_SEL).index(SECTION_SEL) + 1,\r\n\r\n                //caching the value of isResizing at the momment the function is called\r\n                //because it will be checked later inside a setTimeout and the value might change\r\n                localIsResizing: isResizing\r\n            };\r\n\r\n            //quiting when destination scroll is the same as the current one\r\n            if((v.activeSection.is(element) && !isResizing) || (options.scrollBar && $window.scrollTop() === v.dtop && !element.hasClass(AUTO_HEIGHT) )){ return; }\r\n\r\n            if(v.activeSlide.length){\r\n                slideAnchorLink = v.activeSlide.data('anchor');\r\n                slideIndex = v.activeSlide.index();\r\n            }\r\n\r\n            //callback (onLeave) if the site is not just resizing and readjusting the slides\r\n            if($.isFunction(options.onLeave) && !v.localIsResizing){\r\n                var direction = v.yMovement;\r\n\r\n                //required for continousVertical\r\n                if(typeof isMovementUp !== 'undefined'){\r\n                    direction = isMovementUp ? 'up' : 'down';\r\n                }\r\n\r\n                if(options.onLeave.call(v.activeSection, v.leavingSection, (v.sectionIndex + 1), direction) === false){\r\n                    return;\r\n                }\r\n            }\r\n\r\n            // If continuousVertical && we need to wrap around\r\n            if (options.autoScrolling && options.continuousVertical && typeof (v.isMovementUp) !== \"undefined\" &&\r\n                ((!v.isMovementUp && v.yMovement == 'up') || // Intending to scroll down but about to go up or\r\n                (v.isMovementUp && v.yMovement == 'down'))) { // intending to scroll up but about to go down\r\n\r\n                v = createInfiniteSections(v);\r\n            }\r\n\r\n            //pausing media of the leaving section (if we are not just resizing, as destinatino will be the same one)\r\n            if(!v.localIsResizing){\r\n                stopMedia(v.activeSection);\r\n            }\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.beforeLeave();\r\n            }\r\n\r\n            element.addClass(ACTIVE).siblings().removeClass(ACTIVE);\r\n            lazyLoad(element);\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.onLeave();\r\n            }\r\n\r\n            //preventing from activating the MouseWheelHandler event\r\n            //more than once if the page is scrolling\r\n            canScroll = false;\r\n\r\n            setState(slideIndex, slideAnchorLink, v.anchorLink, v.sectionIndex);\r\n\r\n            performMovement(v);\r\n\r\n            //flag to avoid callingn `scrollPage()` twice in case of using anchor links\r\n            lastScrolledDestiny = v.anchorLink;\r\n\r\n            //avoid firing it twice (as it does also on scroll)\r\n            activateMenuAndNav(v.anchorLink, v.sectionIndex);\r\n        }\r\n\r\n        /**\r\n        * Performs the vertical movement (by CSS3 or by jQuery)\r\n        */\r\n        function performMovement(v){\r\n            // using CSS3 translate functionality\r\n            if (options.css3 && options.autoScrolling && !options.scrollBar) {\r\n\r\n                // The first section can have a negative value in iOS 10. Not quite sure why: -0.0142822265625\r\n                // that's why we round it to 0.\r\n                var translate3d = 'translate3d(0px, -' + Math.round(v.dtop) + 'px, 0px)';\r\n                transformContainer(translate3d, true);\r\n\r\n                //even when the scrollingSpeed is 0 there's a little delay, which might cause the\r\n                //scrollingSpeed to change in case of using silentMoveTo();\r\n                if(options.scrollingSpeed){\r\n                    clearTimeout(afterSectionLoadsId);\r\n                    afterSectionLoadsId = setTimeout(function () {\r\n                        afterSectionLoads(v);\r\n                    }, options.scrollingSpeed);\r\n                }else{\r\n                    afterSectionLoads(v);\r\n                }\r\n            }\r\n\r\n            // using jQuery animate\r\n            else{\r\n                var scrollSettings = getScrollSettings(v);\r\n\r\n                $(scrollSettings.element).animate(\r\n                    scrollSettings.options,\r\n                options.scrollingSpeed, options.easing).promise().done(function () { //only one single callback in case of animating  `html, body`\r\n                    if(options.scrollBar){\r\n\r\n                        /* Hack!\r\n                        The timeout prevents setting the most dominant section in the viewport as \"active\" when the user\r\n                        scrolled to a smaller section by using the mousewheel (auto scrolling) rather than draging the scroll bar.\r\n\r\n                        When using scrollBar:true It seems like the scroll events still getting propagated even after the scrolling animation has finished.\r\n                        */\r\n                        setTimeout(function(){\r\n                            afterSectionLoads(v);\r\n                        },30);\r\n                    }else{\r\n                        afterSectionLoads(v);\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the scrolling settings depending on the plugin autoScrolling option\r\n        */\r\n        function getScrollSettings(v){\r\n            var scroll = {};\r\n\r\n            if(options.autoScrolling && !options.scrollBar){\r\n                scroll.options = { 'top': -v.dtop};\r\n                scroll.element = WRAPPER_SEL;\r\n            }else{\r\n                scroll.options = { 'scrollTop': v.dtop};\r\n                scroll.element = 'html, body';\r\n            }\r\n\r\n            return scroll;\r\n        }\r\n\r\n        /**\r\n        * Adds sections before or after the current one to create the infinite effect.\r\n        */\r\n        function createInfiniteSections(v){\r\n            // Scrolling down\r\n            if (!v.isMovementUp) {\r\n                // Move all previous sections to after the active section\r\n                $(SECTION_ACTIVE_SEL).after(v.activeSection.prevAll(SECTION_SEL).get().reverse());\r\n            }\r\n            else { // Scrolling up\r\n                // Move all next sections to before the active section\r\n                $(SECTION_ACTIVE_SEL).before(v.activeSection.nextAll(SECTION_SEL));\r\n            }\r\n\r\n            // Maintain the displayed position (now that we changed the element order)\r\n            silentScroll($(SECTION_ACTIVE_SEL).position().top);\r\n\r\n            // Maintain the active slides visible in the viewport\r\n            keepSlidesPosition();\r\n\r\n            // save for later the elements that still need to be reordered\r\n            v.wrapAroundElements = v.activeSection;\r\n\r\n            // Recalculate animation variables\r\n            v.dtop = v.element.position().top;\r\n            v.yMovement = getYmovement(v.element);\r\n\r\n            //sections will temporally have another position in the DOM\r\n            //updating this values in case we need them\r\n            v.leavingSection = v.activeSection.index(SECTION_SEL) + 1;\r\n            v.sectionIndex = v.element.index(SECTION_SEL);\r\n\r\n            return v;\r\n        }\r\n\r\n        /**\r\n        * Fix section order after continuousVertical changes have been animated\r\n        */\r\n        function continuousVerticalFixSectionOrder (v) {\r\n            // If continuousVertical is in effect (and autoScrolling would also be in effect then),\r\n            // finish moving the elements around so the direct navigation will function more simply\r\n            if (!v.wrapAroundElements || !v.wrapAroundElements.length) {\r\n                return;\r\n            }\r\n\r\n            if (v.isMovementUp) {\r\n                $(SECTION_FIRST_SEL).before(v.wrapAroundElements);\r\n            }\r\n            else {\r\n                $(SECTION_LAST_SEL).after(v.wrapAroundElements);\r\n            }\r\n\r\n            silentScroll($(SECTION_ACTIVE_SEL).position().top);\r\n\r\n            // Maintain the active slides visible in the viewport\r\n            keepSlidesPosition();\r\n        }\r\n\r\n\r\n        /**\r\n        * Actions to do once the section is loaded.\r\n        */\r\n        function afterSectionLoads (v){\r\n            continuousVerticalFixSectionOrder(v);\r\n\r\n            //callback (afterLoad) if the site is not just resizing and readjusting the slides\r\n            $.isFunction(options.afterLoad) && !v.localIsResizing && options.afterLoad.call(v.element, v.anchorLink, (v.sectionIndex + 1));\r\n\r\n            if(options.scrollOverflow){\r\n                options.scrollOverflowHandler.afterLoad();\r\n            }\r\n\r\n            if(!v.localIsResizing){\r\n                playMedia(v.element);\r\n            }\r\n\r\n            v.element.addClass(COMPLETELY).siblings().removeClass(COMPLETELY);\r\n\r\n            canScroll = true;\r\n\r\n            $.isFunction(v.callback) && v.callback.call(this);\r\n        }\r\n\r\n        /**\r\n        * Sets the value for the given attribute from the `data-` attribute with the same suffix\r\n        * ie: data-srcset ==> srcset  |  data-src ==> src\r\n        */\r\n        function setSrc(element, attribute){\r\n            element\r\n                .attr(attribute, element.data(attribute))\r\n                .removeAttr('data-' + attribute);\r\n        }\r\n\r\n        /**\r\n        * Lazy loads image, video and audio elements.\r\n        */\r\n        function lazyLoad(destiny){\r\n            if (!options.lazyLoading){\r\n                return;\r\n            }\r\n\r\n            var panel = getSlideOrSection(destiny);\r\n            var element;\r\n\r\n            panel.find('img[data-src], img[data-srcset], source[data-src], source[data-srcset], video[data-src], audio[data-src], iframe[data-src]').each(function(){\r\n                element = $(this);\r\n\r\n                $.each(['src', 'srcset'], function(index, type){\r\n                    var attribute = element.attr('data-' + type);\r\n                    if(typeof attribute !== 'undefined' && attribute){\r\n                        setSrc(element, type);\r\n                    }\r\n                });\r\n\r\n                if(element.is('source')){\r\n                    var typeToPlay = element.closest('video').length ? 'video' : 'audio';\r\n                    element.closest(typeToPlay).get(0).load();\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Plays video and audio elements.\r\n        */\r\n        function playMedia(destiny){\r\n            var panel = getSlideOrSection(destiny);\r\n\r\n            //playing HTML5 media elements\r\n            panel.find('video, audio').each(function(){\r\n                var element = $(this).get(0);\r\n\r\n                if( element.hasAttribute('data-autoplay') && typeof element.play === 'function' ) {\r\n                    element.play();\r\n                }\r\n            });\r\n\r\n            //youtube videos\r\n            panel.find('iframe[src*=\"youtube.com/embed/\"]').each(function(){\r\n                var element = $(this).get(0);\r\n\r\n                if ( element.hasAttribute('data-autoplay') ){\r\n                    playYoutube(element);\r\n                }\r\n\r\n                //in case the URL was not loaded yet. On page load we need time for the new URL (with the API string) to load.\r\n                element.onload = function() {\r\n                    if ( element.hasAttribute('data-autoplay') ){\r\n                        playYoutube(element);\r\n                    }\r\n                };\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Plays a youtube video\r\n        */\r\n        function playYoutube(element){\r\n            element.contentWindow.postMessage('{\"event\":\"command\",\"func\":\"playVideo\",\"args\":\"\"}', '*');\r\n        }\r\n\r\n        /**\r\n        * Stops video and audio elements.\r\n        */\r\n        function stopMedia(destiny){\r\n            var panel = getSlideOrSection(destiny);\r\n\r\n            //stopping HTML5 media elements\r\n            panel.find('video, audio').each(function(){\r\n                var element = $(this).get(0);\r\n\r\n                if( !element.hasAttribute('data-keepplaying') && typeof element.pause === 'function' ) {\r\n                    element.pause();\r\n                }\r\n            });\r\n\r\n            //youtube videos\r\n            panel.find('iframe[src*=\"youtube.com/embed/\"]').each(function(){\r\n                var element = $(this).get(0);\r\n\r\n                if( /youtube\\.com\\/embed\\//.test($(this).attr('src')) && !element.hasAttribute('data-keepplaying')){\r\n                    $(this).get(0).contentWindow.postMessage('{\"event\":\"command\",\"func\":\"pauseVideo\",\"args\":\"\"}','*');\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Gets the active slide (or section) for the given section\r\n        */\r\n        function getSlideOrSection(destiny){\r\n            var slide = destiny.find(SLIDE_ACTIVE_SEL);\r\n            if( slide.length ) {\r\n                destiny = $(slide);\r\n            }\r\n\r\n            return destiny;\r\n        }\r\n\r\n        /**\r\n        * Scrolls to the anchor in the URL when loading the site\r\n        */\r\n        function scrollToAnchor(){\r\n            var anchors =  getAnchorsURL();\r\n            var sectionAnchor = anchors.section;\r\n            var slideAnchor = anchors.slide;\r\n\r\n            if(sectionAnchor){  //if theres any #\r\n                if(options.animateAnchor){\r\n                    scrollPageAndSlide(sectionAnchor, slideAnchor);\r\n                }else{\r\n                    silentMoveTo(sectionAnchor, slideAnchor);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Detecting any change on the URL to scroll to the given anchor link\r\n        * (a way to detect back history button as we play with the hashes on the URL)\r\n        */\r\n        function hashChangeHandler(){\r\n            if(!isScrolling && !options.lockAnchors){\r\n                var anchors = getAnchorsURL();\r\n                var sectionAnchor = anchors.section;\r\n                var slideAnchor = anchors.slide;\r\n\r\n                //when moving to a slide in the first section for the first time (first time to add an anchor to the URL)\r\n                var isFirstSlideMove =  (typeof lastScrolledDestiny === 'undefined');\r\n                var isFirstScrollMove = (typeof lastScrolledDestiny === 'undefined' && typeof slideAnchor === 'undefined' && !slideMoving);\r\n\r\n                if(sectionAnchor && sectionAnchor.length){\r\n                    /*in order to call scrollpage() only once for each destination at a time\r\n                    It is called twice for each scroll otherwise, as in case of using anchorlinks `hashChange`\r\n                    event is fired on every scroll too.*/\r\n                    if ((sectionAnchor && sectionAnchor !== lastScrolledDestiny) && !isFirstSlideMove\r\n                        || isFirstScrollMove\r\n                        || (!slideMoving && lastScrolledSlide != slideAnchor )){\r\n\r\n                        scrollPageAndSlide(sectionAnchor, slideAnchor);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        //gets the URL anchors (section and slide)\r\n        function getAnchorsURL(){\r\n            var section;\r\n            var slide;\r\n            var hash = window.location.hash;\r\n\r\n            if(hash.length){\r\n                //getting the anchor link in the URL and deleting the `#`\r\n                var anchorsParts =  hash.replace('#', '').split('/');\r\n\r\n                //using / for visual reasons and not as a section/slide separator #2803\r\n                var isFunkyAnchor = hash.indexOf('#/') > -1;\r\n\r\n                section = isFunkyAnchor ? '/' + anchorsParts[1] : decodeURIComponent(anchorsParts[0]);\r\n\r\n                var slideAnchor = isFunkyAnchor ? anchorsParts[2] : anchorsParts[1];\r\n                if(slideAnchor && slideAnchor.length){\r\n                    slide = decodeURIComponent(slideAnchor);\r\n                }\r\n            }\r\n\r\n            return {\r\n                section: section,\r\n                slide: slide\r\n            }\r\n        }\r\n\r\n        //Sliding with arrow keys, both, vertical and horizontal\r\n        function keydownHandler(e) {\r\n            clearTimeout(keydownId);\r\n\r\n            var activeElement = $(':focus');\r\n            var keyCode = e.which;\r\n\r\n            //tab?\r\n            if(keyCode === 9){\r\n                onTab(e);\r\n            }\r\n\r\n            else if(!activeElement.is('textarea') && !activeElement.is('input') && !activeElement.is('select') &&\r\n                activeElement.attr('contentEditable') !== \"true\" && activeElement.attr('contentEditable') !== '' &&\r\n                options.keyboardScrolling && options.autoScrolling){\r\n\r\n                //preventing the scroll with arrow keys & spacebar & Page Up & Down keys\r\n                var keyControls = [40, 38, 32, 33, 34];\r\n                if($.inArray(keyCode, keyControls) > -1){\r\n                    e.preventDefault();\r\n                }\r\n\r\n                controlPressed = e.ctrlKey;\r\n\r\n                keydownId = setTimeout(function(){\r\n                    onkeydown(e);\r\n                },150);\r\n            }\r\n        }\r\n\r\n        function tooltipTextHandler(){\r\n            $(this).prev().trigger('click');\r\n        }\r\n\r\n        //to prevent scrolling while zooming\r\n        function keyUpHandler(e){\r\n            if(isWindowFocused){ //the keyup gets fired on new tab ctrl + t in Firefox\r\n                controlPressed = e.ctrlKey;\r\n            }\r\n        }\r\n\r\n        //binding the mousemove when the mouse's middle button is released\r\n        function mouseDownHandler(e){\r\n            //middle button\r\n            if (e.which == 2){\r\n                oldPageY = e.pageY;\r\n                container.on('mousemove', mouseMoveHandler);\r\n            }\r\n        }\r\n\r\n        //unbinding the mousemove when the mouse's middle button is released\r\n        function mouseUpHandler(e){\r\n            //middle button\r\n            if (e.which == 2){\r\n                container.off('mousemove');\r\n            }\r\n        }\r\n\r\n        //Scrolling horizontally when clicking on the slider controls.\r\n        function slideArrowHandler(){\r\n            var section = $(this).closest(SECTION_SEL);\r\n\r\n            if ($(this).hasClass(SLIDES_PREV)) {\r\n                if(isScrollAllowed.m.left){\r\n                    moveSlideLeft(section);\r\n                }\r\n            } else {\r\n                if(isScrollAllowed.m.right){\r\n                    moveSlideRight(section);\r\n                }\r\n            }\r\n        }\r\n\r\n        //when opening a new tab (ctrl + t), `control` won't be pressed when coming back.\r\n        function blurHandler(){\r\n            isWindowFocused = false;\r\n            controlPressed = false;\r\n        }\r\n\r\n        //Scrolls to the section when clicking the navigation bullet\r\n        function sectionBulletHandler(e){\r\n            e.preventDefault();\r\n            var index = $(this).parent().index();\r\n            scrollPage($(SECTION_SEL).eq(index));\r\n        }\r\n\r\n        //Scrolls the slider to the given slide destination for the given section\r\n        function slideBulletHandler(e){\r\n            e.preventDefault();\r\n            var slides = $(this).closest(SECTION_SEL).find(SLIDES_WRAPPER_SEL);\r\n            var destiny = slides.find(SLIDE_SEL).eq($(this).closest('li').index());\r\n\r\n            landscapeScroll(slides, destiny);\r\n        }\r\n\r\n        /**\r\n        * Keydown event\r\n        */\r\n        function onkeydown(e){\r\n            var shiftPressed = e.shiftKey;\r\n\r\n            //do nothing if we can not scroll or we are not using horizotnal key arrows.\r\n            if(!canScroll && [37,39].indexOf(e.which) < 0){\r\n                return;\r\n            }\r\n\r\n            switch (e.which) {\r\n                //up\r\n                case 38:\r\n                case 33:\r\n                    if(isScrollAllowed.k.up){\r\n                        moveSectionUp();\r\n                    }\r\n                    break;\r\n\r\n                //down\r\n                case 32: //spacebar\r\n                    if(shiftPressed && isScrollAllowed.k.up){\r\n                        moveSectionUp();\r\n                        break;\r\n                    }\r\n                /* falls through */\r\n                case 40:\r\n                case 34:\r\n                    if(isScrollAllowed.k.down){\r\n                        moveSectionDown();\r\n                    }\r\n                    break;\r\n\r\n                //Home\r\n                case 36:\r\n                    if(isScrollAllowed.k.up){\r\n                        moveTo(1);\r\n                    }\r\n                    break;\r\n\r\n                //End\r\n                case 35:\r\n                     if(isScrollAllowed.k.down){\r\n                        moveTo( $(SECTION_SEL).length );\r\n                    }\r\n                    break;\r\n\r\n                //left\r\n                case 37:\r\n                    if(isScrollAllowed.k.left){\r\n                        moveSlideLeft();\r\n                    }\r\n                    break;\r\n\r\n                //right\r\n                case 39:\r\n                    if(isScrollAllowed.k.right){\r\n                        moveSlideRight();\r\n                    }\r\n                    break;\r\n\r\n                default:\r\n                    return; // exit this handler for other keys\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Makes sure the tab key will only focus elements within the current section/slide\r\n        * preventing this way from breaking the page.\r\n        * Based on \"Modals and keyboard traps\"\r\n        * from https://developers.google.com/web/fundamentals/accessibility/focus/using-tabindex\r\n        */\r\n        function onTab(e){\r\n            var isShiftPressed = e.shiftKey;\r\n            var activeElement = $(':focus');\r\n            var activeSection = $(SECTION_ACTIVE_SEL);\r\n            var activeSlide = activeSection.find(SLIDE_ACTIVE_SEL);\r\n            var focusableWrapper = activeSlide.length ? activeSlide : activeSection;\r\n            var focusableElements = focusableWrapper.find(focusableElementsString).not('[tabindex=\"-1\"]');\r\n\r\n            function preventAndFocusFirst(e){\r\n                e.preventDefault();\r\n                return focusableElements.first().focus();\r\n            }\r\n\r\n            //is there an element with focus?\r\n            if(activeElement.length){\r\n                if(!activeElement.closest(SECTION_ACTIVE_SEL, SLIDE_ACTIVE_SEL).length){\r\n                    activeElement = preventAndFocusFirst(e);\r\n                }\r\n            }\r\n\r\n            //no element if focused? Let's focus the first one of the section/slide\r\n            else{\r\n                preventAndFocusFirst(e);\r\n            }\r\n\r\n            //when reached the first or last focusable element of the section/slide\r\n            //we prevent the tab action to keep it in the last focusable element\r\n            if(!isShiftPressed && activeElement.is(focusableElements.last()) ||\r\n                isShiftPressed && activeElement.is(focusableElements.first())\r\n            ){\r\n                e.preventDefault();\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Detecting the direction of the mouse movement.\r\n        * Used only for the middle button of the mouse.\r\n        */\r\n        var oldPageY = 0;\r\n        function mouseMoveHandler(e){\r\n            if(canScroll){\r\n                // moving up\r\n                if (e.pageY < oldPageY && isScrollAllowed.m.up){\r\n                    moveSectionUp();\r\n                }\r\n\r\n                // moving down\r\n                else if(e.pageY > oldPageY && isScrollAllowed.m.down){\r\n                    moveSectionDown();\r\n                }\r\n            }\r\n            oldPageY = e.pageY;\r\n        }\r\n\r\n        /**\r\n        * Scrolls horizontal sliders.\r\n        */\r\n        function landscapeScroll(slides, destiny, direction){\r\n            var section = slides.closest(SECTION_SEL);\r\n            var v = {\r\n                slides: slides,\r\n                destiny: destiny,\r\n                direction: direction,\r\n                destinyPos: destiny.position(),\r\n                slideIndex: destiny.index(),\r\n                section: section,\r\n                sectionIndex: section.index(SECTION_SEL),\r\n                anchorLink: section.data('anchor'),\r\n                slidesNav: section.find(SLIDES_NAV_SEL),\r\n                slideAnchor:  getAnchor(destiny),\r\n                prevSlide: section.find(SLIDE_ACTIVE_SEL),\r\n                prevSlideIndex: section.find(SLIDE_ACTIVE_SEL).index(),\r\n\r\n                //caching the value of isResizing at the momment the function is called\r\n                //because it will be checked later inside a setTimeout and the value might change\r\n                localIsResizing: isResizing\r\n            };\r\n            v.xMovement = getXmovement(v.prevSlideIndex, v.slideIndex);\r\n\r\n            //important!! Only do it when not resizing\r\n            if(!v.localIsResizing){\r\n                //preventing from scrolling to the next/prev section when using scrollHorizontally\r\n                canScroll = false;\r\n            }\r\n\r\n            if(options.onSlideLeave){\r\n\r\n                //if the site is not just resizing and readjusting the slides\r\n                if(!v.localIsResizing && v.xMovement!=='none'){\r\n                    if($.isFunction( options.onSlideLeave )){\r\n                        if(options.onSlideLeave.call( v.prevSlide, v.anchorLink, (v.sectionIndex + 1), v.prevSlideIndex, v.direction, v.slideIndex ) === false){\r\n                            slideMoving = false;\r\n                            return;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            destiny.addClass(ACTIVE).siblings().removeClass(ACTIVE);\r\n\r\n            if(!v.localIsResizing){\r\n                stopMedia(v.prevSlide);\r\n                lazyLoad(destiny);\r\n            }\r\n\r\n            if(!options.loopHorizontal && options.controlArrows){\r\n                //hidding it for the fist slide, showing for the rest\r\n                section.find(SLIDES_ARROW_PREV_SEL).toggle(v.slideIndex!==0);\r\n\r\n                //hidding it for the last slide, showing for the rest\r\n                section.find(SLIDES_ARROW_NEXT_SEL).toggle(!destiny.is(':last-child'));\r\n            }\r\n\r\n            //only changing the URL if the slides are in the current section (not for resize re-adjusting)\r\n            if(section.hasClass(ACTIVE) && !v.localIsResizing){\r\n                setState(v.slideIndex, v.slideAnchor, v.anchorLink, v.sectionIndex);\r\n            }\r\n\r\n            performHorizontalMove(slides, v, true);\r\n        }\r\n\r\n\r\n        function afterSlideLoads(v){\r\n            activeSlidesNavigation(v.slidesNav, v.slideIndex);\r\n\r\n            //if the site is not just resizing and readjusting the slides\r\n            if(!v.localIsResizing){\r\n                $.isFunction( options.afterSlideLoad ) && options.afterSlideLoad.call( v.destiny, v.anchorLink, (v.sectionIndex + 1), v.slideAnchor, v.slideIndex);\r\n\r\n                //needs to be inside the condition to prevent problems with continuousVertical and scrollHorizontally\r\n                //and to prevent double scroll right after a windows resize\r\n                canScroll = true;\r\n\r\n                playMedia(v.destiny);\r\n            }\r\n\r\n            //letting them slide again\r\n            slideMoving = false;\r\n        }\r\n\r\n        /**\r\n        * Performs the horizontal movement. (CSS3 or jQuery)\r\n        *\r\n        * @param fireCallback {Bool} - determines whether or not to fire the callback\r\n        */\r\n        function performHorizontalMove(slides, v, fireCallback){\r\n            var destinyPos = v.destinyPos;\r\n\r\n            if(options.css3){\r\n                var translate3d = 'translate3d(-' + Math.round(destinyPos.left) + 'px, 0px, 0px)';\r\n\r\n                addAnimation(slides.find(SLIDES_CONTAINER_SEL)).css(getTransforms(translate3d));\r\n\r\n                afterSlideLoadsId = setTimeout(function(){\r\n                    fireCallback && afterSlideLoads(v);\r\n                }, options.scrollingSpeed, options.easing);\r\n            }else{\r\n                slides.animate({\r\n                    scrollLeft : Math.round(destinyPos.left)\r\n                }, options.scrollingSpeed, options.easing, function() {\r\n\r\n                    fireCallback && afterSlideLoads(v);\r\n                });\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets the state for the horizontal bullet navigations.\r\n        */\r\n        function activeSlidesNavigation(slidesNav, slideIndex){\r\n            slidesNav.find(ACTIVE_SEL).removeClass(ACTIVE);\r\n            slidesNav.find('li').eq(slideIndex).find('a').addClass(ACTIVE);\r\n        }\r\n\r\n        var previousHeight = windowsHeight;\r\n\r\n        //when resizing the site, we adjust the heights of the sections, slimScroll...\r\n        function resizeHandler(){\r\n            //checking if it needs to get responsive\r\n            responsive();\r\n\r\n            // rebuild immediately on touch devices\r\n            if (isTouchDevice) {\r\n                var activeElement = $(document.activeElement);\r\n\r\n                //if the keyboard is NOT visible\r\n                if (!activeElement.is('textarea') && !activeElement.is('input') && !activeElement.is('select')) {\r\n                    var currentHeight = $window.height();\r\n\r\n                    //making sure the change in the viewport size is enough to force a rebuild. (20 % of the window to avoid problems when hidding scroll bars)\r\n                    if( Math.abs(currentHeight - previousHeight) > (20 * Math.max(previousHeight, currentHeight) / 100) ){\r\n                        reBuild(true);\r\n                        previousHeight = currentHeight;\r\n                    }\r\n                }\r\n            }else{\r\n                //in order to call the functions only when the resize is finished\r\n                //http://stackoverflow.com/questions/4298612/jquery-how-to-call-resize-event-only-once-its-finished-resizing\r\n                clearTimeout(resizeId);\r\n\r\n                resizeId = setTimeout(function(){\r\n                    reBuild(true);\r\n                }, 350);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Checks if the site needs to get responsive and disables autoScrolling if so.\r\n        * A class `fp-responsive` is added to the plugin's container in case the user wants to use it for his own responsive CSS.\r\n        */\r\n        function responsive(){\r\n            var widthLimit = options.responsive || options.responsiveWidth; //backwards compatiblity\r\n            var heightLimit = options.responsiveHeight;\r\n\r\n            //only calculating what we need. Remember its called on the resize event.\r\n            var isBreakingPointWidth = widthLimit && $window.outerWidth() < widthLimit;\r\n            var isBreakingPointHeight = heightLimit && $window.height() < heightLimit;\r\n\r\n            if(widthLimit && heightLimit){\r\n                setResponsive(isBreakingPointWidth || isBreakingPointHeight);\r\n            }\r\n            else if(widthLimit){\r\n                setResponsive(isBreakingPointWidth);\r\n            }\r\n            else if(heightLimit){\r\n                setResponsive(isBreakingPointHeight);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds transition animations for the given element\r\n        */\r\n        function addAnimation(element){\r\n            var transition = 'all ' + options.scrollingSpeed + 'ms ' + options.easingcss3;\r\n\r\n            element.removeClass(NO_TRANSITION);\r\n            return element.css({\r\n                '-webkit-transition': transition,\r\n                'transition': transition\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Remove transition animations for the given element\r\n        */\r\n        function removeAnimation(element){\r\n            return element.addClass(NO_TRANSITION);\r\n        }\r\n\r\n        /**\r\n        * Activating the vertical navigation bullets according to the given slide name.\r\n        */\r\n        function activateNavDots(name, sectionIndex){\r\n            if(options.navigation){\r\n                $(SECTION_NAV_SEL).find(ACTIVE_SEL).removeClass(ACTIVE);\r\n                if(name){\r\n                    $(SECTION_NAV_SEL).find('a[href=\"#' + name + '\"]').addClass(ACTIVE);\r\n                }else{\r\n                    $(SECTION_NAV_SEL).find('li').eq(sectionIndex).find('a').addClass(ACTIVE);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Activating the website main menu elements according to the given slide name.\r\n        */\r\n        function activateMenuElement(name){\r\n            if(options.menu){\r\n                $(options.menu).find(ACTIVE_SEL).removeClass(ACTIVE);\r\n                $(options.menu).find('[data-menuanchor=\"'+name+'\"]').addClass(ACTIVE);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Sets to active the current menu and vertical nav items.\r\n        */\r\n        function activateMenuAndNav(anchor, index){\r\n            activateMenuElement(anchor);\r\n            activateNavDots(anchor, index);\r\n        }\r\n\r\n        /**\r\n        * Retuns `up` or `down` depending on the scrolling movement to reach its destination\r\n        * from the current section.\r\n        */\r\n        function getYmovement(destiny){\r\n            var fromIndex = $(SECTION_ACTIVE_SEL).index(SECTION_SEL);\r\n            var toIndex = destiny.index(SECTION_SEL);\r\n            if( fromIndex == toIndex){\r\n                return 'none';\r\n            }\r\n            if(fromIndex > toIndex){\r\n                return 'up';\r\n            }\r\n            return 'down';\r\n        }\r\n\r\n        /**\r\n        * Retuns `right` or `left` depending on the scrolling movement to reach its destination\r\n        * from the current slide.\r\n        */\r\n        function getXmovement(fromIndex, toIndex){\r\n            if( fromIndex == toIndex){\r\n                return 'none';\r\n            }\r\n            if(fromIndex > toIndex){\r\n                return 'left';\r\n            }\r\n            return 'right';\r\n        }\r\n\r\n        function addTableClass(element){\r\n            //In case we are styling for the 2nd time as in with reponsiveSlides\r\n            if(!element.hasClass(TABLE)){\r\n                var wrapper = $('<div class=\"' + TABLE_CELL + '\" />').height(getTableHeight(element));\r\n                element.addClass(TABLE).wrapInner(wrapper);\r\n            }\r\n        }\r\n\r\n        function getTableHeight(element){\r\n            var sectionHeight = windowsHeight;\r\n\r\n            if(options.paddingTop || options.paddingBottom){\r\n                var section = element;\r\n                if(!section.hasClass(SECTION)){\r\n                    section = element.closest(SECTION_SEL);\r\n                }\r\n\r\n                var paddings = parseInt(section.css('padding-top')) + parseInt(section.css('padding-bottom'));\r\n                sectionHeight = (windowsHeight - paddings);\r\n            }\r\n\r\n            return sectionHeight;\r\n        }\r\n\r\n        /**\r\n        * Adds a css3 transform property to the container class with or without animation depending on the animated param.\r\n        */\r\n        function transformContainer(translate3d, animated){\r\n            if(animated){\r\n                addAnimation(container);\r\n            }else{\r\n                removeAnimation(container);\r\n            }\r\n\r\n            container.css(getTransforms(translate3d));\r\n\r\n            //syncronously removing the class after the animation has been applied.\r\n            setTimeout(function(){\r\n                container.removeClass(NO_TRANSITION);\r\n            },10);\r\n        }\r\n\r\n        /**\r\n        * Gets a section by its anchor / index\r\n        */\r\n        function getSectionByAnchor(sectionAnchor){\r\n            var section = container.find(SECTION_SEL + '[data-anchor=\"'+sectionAnchor+'\"]');\r\n            if(!section.length){\r\n                var sectionIndex = typeof sectionAnchor !== 'undefined' ? sectionAnchor -1 : 0;\r\n                section = $(SECTION_SEL).eq(sectionIndex);\r\n            }\r\n\r\n            return section;\r\n        }\r\n\r\n        /**\r\n        * Gets a slide inside a given section by its anchor / index\r\n        */\r\n        function getSlideByAnchor(slideAnchor, section){\r\n            var slide = section.find(SLIDE_SEL + '[data-anchor=\"'+slideAnchor+'\"]');\r\n            if(!slide.length){\r\n                slideAnchor = typeof slideAnchor !== 'undefined' ? slideAnchor : 0;\r\n                slide = section.find(SLIDE_SEL).eq(slideAnchor);\r\n            }\r\n\r\n            return slide;\r\n        }\r\n\r\n        /**\r\n        * Scrolls to the given section and slide anchors\r\n        */\r\n        function scrollPageAndSlide(sectionAnchor, slideAnchor){\r\n            var section = getSectionByAnchor(sectionAnchor);\r\n\r\n            //do nothing if there's no section with the given anchor name\r\n            if(!section.length) return;\r\n\r\n            var slide = getSlideByAnchor(slideAnchor, section);\r\n\r\n            //we need to scroll to the section and then to the slide\r\n            if (sectionAnchor !== lastScrolledDestiny && !section.hasClass(ACTIVE)){\r\n                scrollPage(section, function(){\r\n                    scrollSlider(slide);\r\n                });\r\n            }\r\n            //if we were already in the section\r\n            else{\r\n                scrollSlider(slide);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Scrolls the slider to the given slide destination for the given section\r\n        */\r\n        function scrollSlider(slide){\r\n            if(slide.length){\r\n                landscapeScroll(slide.closest(SLIDES_WRAPPER_SEL), slide);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Creates a landscape navigation bar with dots for horizontal sliders.\r\n        */\r\n        function addSlidesNavigation(section, numSlides){\r\n            section.append('<div class=\"' + SLIDES_NAV + '\"><ul></ul></div>');\r\n            var nav = section.find(SLIDES_NAV_SEL);\r\n\r\n            //top or bottom\r\n            nav.addClass(options.slidesNavPosition);\r\n\r\n            for(var i=0; i< numSlides; i++){\r\n                nav.find('ul').append('<li><a href=\"#\"><span></span></a></li>');\r\n            }\r\n\r\n            //centering it\r\n            nav.css('margin-left', '-' + (nav.width()/2) + 'px');\r\n\r\n            nav.find('li').first().find('a').addClass(ACTIVE);\r\n        }\r\n\r\n\r\n        /**\r\n        * Sets the state of the website depending on the active section/slide.\r\n        * It changes the URL hash when needed and updates the body class.\r\n        */\r\n        function setState(slideIndex, slideAnchor, anchorLink, sectionIndex){\r\n            var sectionHash = '';\r\n\r\n            if(options.anchors.length && !options.lockAnchors){\r\n\r\n                //isn't it the first slide?\r\n                if(slideIndex){\r\n                    if(typeof anchorLink !== 'undefined'){\r\n                        sectionHash = anchorLink;\r\n                    }\r\n\r\n                    //slide without anchor link? We take the index instead.\r\n                    if(typeof slideAnchor === 'undefined'){\r\n                        slideAnchor = slideIndex;\r\n                    }\r\n\r\n                    lastScrolledSlide = slideAnchor;\r\n                    setUrlHash(sectionHash + '/' + slideAnchor);\r\n\r\n                //first slide won't have slide anchor, just the section one\r\n                }else if(typeof slideIndex !== 'undefined'){\r\n                    lastScrolledSlide = slideAnchor;\r\n                    setUrlHash(anchorLink);\r\n                }\r\n\r\n                //section without slides\r\n                else{\r\n                    setUrlHash(anchorLink);\r\n                }\r\n            }\r\n\r\n            setBodyClass();\r\n        }\r\n\r\n        /**\r\n        * Sets the URL hash.\r\n        */\r\n        function setUrlHash(url){\r\n            if(options.recordHistory){\r\n                location.hash = url;\r\n            }else{\r\n                //Mobile Chrome doesn't work the normal way, so... lets use HTML5 for phones :)\r\n                if(isTouchDevice || isTouch){\r\n                    window.history.replaceState(undefined, undefined, '#' + url);\r\n                }else{\r\n                    var baseUrl = window.location.href.split('#')[0];\r\n                    window.location.replace( baseUrl + '#' + url );\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Gets the anchor for the given slide / section. Its index will be used if there's none.\r\n        */\r\n        function getAnchor(element){\r\n            var anchor = element.data('anchor');\r\n            var index = element.index();\r\n\r\n            //Slide without anchor link? We take the index instead.\r\n            if(typeof anchor === 'undefined'){\r\n                anchor = index;\r\n            }\r\n\r\n            return anchor;\r\n        }\r\n\r\n        /**\r\n        * Sets a class for the body of the page depending on the active section / slide\r\n        */\r\n        function setBodyClass(){\r\n            var section = $(SECTION_ACTIVE_SEL);\r\n            var slide = section.find(SLIDE_ACTIVE_SEL);\r\n\r\n            var sectionAnchor = getAnchor(section);\r\n            var slideAnchor = getAnchor(slide);\r\n\r\n            var text = String(sectionAnchor);\r\n\r\n            if(slide.length){\r\n                text = text + '-' + slideAnchor;\r\n            }\r\n\r\n            //changing slash for dash to make it a valid CSS style\r\n            text = text.replace('/', '-').replace('#','');\r\n\r\n            //removing previous anchor classes\r\n            var classRe = new RegExp('\\\\b\\\\s?' + VIEWING_PREFIX + '-[^\\\\s]+\\\\b', \"g\");\r\n            $body[0].className = $body[0].className.replace(classRe, '');\r\n\r\n            //adding the current anchor\r\n            $body.addClass(VIEWING_PREFIX + '-' + text);\r\n        }\r\n\r\n        /**\r\n        * Checks for translate3d support\r\n        * @return boolean\r\n        * http://stackoverflow.com/questions/5661671/detecting-transform-translate3d-support\r\n        */\r\n        function support3d() {\r\n            var el = document.createElement('p'),\r\n                has3d,\r\n                transforms = {\r\n                    'webkitTransform':'-webkit-transform',\r\n                    'OTransform':'-o-transform',\r\n                    'msTransform':'-ms-transform',\r\n                    'MozTransform':'-moz-transform',\r\n                    'transform':'transform'\r\n                };\r\n\r\n            // Add it to the body to get the computed style.\r\n            document.body.insertBefore(el, null);\r\n\r\n            for (var t in transforms) {\r\n                if (el.style[t] !== undefined) {\r\n                    el.style[t] = 'translate3d(1px,1px,1px)';\r\n                    has3d = window.getComputedStyle(el).getPropertyValue(transforms[t]);\r\n                }\r\n            }\r\n\r\n            document.body.removeChild(el);\r\n\r\n            return (has3d !== undefined && has3d.length > 0 && has3d !== 'none');\r\n        }\r\n\r\n        /**\r\n        * Removes the auto scrolling action fired by the mouse wheel and trackpad.\r\n        * After this function is called, the mousewheel and trackpad movements won't scroll through sections.\r\n        */\r\n        function removeMouseWheelHandler(){\r\n            if (document.addEventListener) {\r\n                document.removeEventListener('mousewheel', MouseWheelHandler, false); //IE9, Chrome, Safari, Oper\r\n                document.removeEventListener('wheel', MouseWheelHandler, false); //Firefox\r\n                document.removeEventListener('MozMousePixelScroll', MouseWheelHandler, false); //old Firefox\r\n            } else {\r\n                document.detachEvent('onmousewheel', MouseWheelHandler); //IE 6/7/8\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Adds the auto scrolling action for the mouse wheel and trackpad.\r\n        * After this function is called, the mousewheel and trackpad movements will scroll through sections\r\n        * https://developer.mozilla.org/en-US/docs/Web/Events/wheel\r\n        */\r\n        function addMouseWheelHandler(){\r\n            var prefix = '';\r\n            var _addEventListener;\r\n\r\n            if (window.addEventListener){\r\n                _addEventListener = \"addEventListener\";\r\n            }else{\r\n                _addEventListener = \"attachEvent\";\r\n                prefix = 'on';\r\n            }\r\n\r\n             // detect available wheel event\r\n            var support = 'onwheel' in document.createElement('div') ? 'wheel' : // Modern browsers support \"wheel\"\r\n                      document.onmousewheel !== undefined ? 'mousewheel' : // Webkit and IE support at least \"mousewheel\"\r\n                      'DOMMouseScroll'; // let's assume that remaining browsers are older Firefox\r\n\r\n\r\n            if(support == 'DOMMouseScroll'){\r\n                document[ _addEventListener ](prefix + 'MozMousePixelScroll', MouseWheelHandler, false);\r\n            }\r\n\r\n            //handle MozMousePixelScroll in older Firefox\r\n            else{\r\n                document[ _addEventListener ](prefix + support, MouseWheelHandler, false);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Binding the mousemove when the mouse's middle button is pressed\r\n        */\r\n        function addMiddleWheelHandler(){\r\n            container\r\n                .on('mousedown', mouseDownHandler)\r\n                .on('mouseup', mouseUpHandler);\r\n        }\r\n\r\n        /**\r\n        * Unbinding the mousemove when the mouse's middle button is released\r\n        */\r\n        function removeMiddleWheelHandler(){\r\n            container\r\n                .off('mousedown', mouseDownHandler)\r\n                .off('mouseup', mouseUpHandler);\r\n        }\r\n\r\n        /**\r\n        * Adds the possibility to auto scroll through sections on touch devices.\r\n        */\r\n        function addTouchHandler(){\r\n            if(isTouchDevice || isTouch){\r\n                if(options.autoScrolling){\r\n                    $body.off(events.touchmove).on(events.touchmove, preventBouncing);\r\n                }\r\n\r\n                $(WRAPPER_SEL)\r\n                    .off(events.touchstart).on(events.touchstart, touchStartHandler)\r\n                    .off(events.touchmove).on(events.touchmove, touchMoveHandler);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Removes the auto scrolling for touch devices.\r\n        */\r\n        function removeTouchHandler(){\r\n            if(isTouchDevice || isTouch){\r\n                if(options.autoScrolling){\r\n                    $body.off(events.touchmove);\r\n                }\r\n\r\n                $(WRAPPER_SEL)\r\n                    .off(events.touchstart)\r\n                    .off(events.touchmove);\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Returns and object with Microsoft pointers (for IE<11 and for IE >= 11)\r\n        * http://msdn.microsoft.com/en-us/library/ie/dn304886(v=vs.85).aspx\r\n        */\r\n        function getMSPointer(){\r\n            var pointer;\r\n\r\n            //IE >= 11 & rest of browsers\r\n            if(window.PointerEvent){\r\n                pointer = { down: 'pointerdown', move: 'pointermove'};\r\n            }\r\n\r\n            //IE < 11\r\n            else{\r\n                pointer = { down: 'MSPointerDown', move: 'MSPointerMove'};\r\n            }\r\n\r\n            return pointer;\r\n        }\r\n\r\n        /**\r\n        * Gets the pageX and pageY properties depending on the browser.\r\n        * https://github.com/alvarotrigo/fullPage.js/issues/194#issuecomment-34069854\r\n        */\r\n        function getEventsPage(e){\r\n            var events = [];\r\n\r\n            events.y = (typeof e.pageY !== 'undefined' && (e.pageY || e.pageX) ? e.pageY : e.touches[0].pageY);\r\n            events.x = (typeof e.pageX !== 'undefined' && (e.pageY || e.pageX) ? e.pageX : e.touches[0].pageX);\r\n\r\n            //in touch devices with scroll bar, e.pageY is detected, but we have to deal with touch events. #1008\r\n            if(isTouch && isReallyTouch(e) && (options.scrollBar || !options.autoScrolling)){\r\n                events.y = e.touches[0].pageY;\r\n                events.x = e.touches[0].pageX;\r\n            }\r\n\r\n            return events;\r\n        }\r\n\r\n        /**\r\n        * Slides silently (with no animation) the active slider to the given slide.\r\n        * @param noCallback {bool} true or defined -> no callbacks\r\n        */\r\n        function silentLandscapeScroll(activeSlide, noCallbacks){\r\n            setScrollingSpeed (0, 'internal');\r\n\r\n            if(typeof noCallbacks !== 'undefined'){\r\n                //preventing firing callbacks afterSlideLoad etc.\r\n                isResizing = true;\r\n            }\r\n\r\n            landscapeScroll(activeSlide.closest(SLIDES_WRAPPER_SEL), activeSlide);\r\n\r\n            if(typeof noCallbacks !== 'undefined'){\r\n                isResizing = false;\r\n            }\r\n\r\n            setScrollingSpeed(originals.scrollingSpeed, 'internal');\r\n        }\r\n\r\n        /**\r\n        * Scrolls silently (with no animation) the page to the given Y position.\r\n        */\r\n        function silentScroll(top){\r\n            // The first section can have a negative value in iOS 10. Not quite sure why: -0.0142822265625\r\n            // that's why we round it to 0.\r\n            var roundedTop = Math.round(top);\r\n\r\n            if (options.css3 && options.autoScrolling && !options.scrollBar){\r\n                var translate3d = 'translate3d(0px, -' + roundedTop + 'px, 0px)';\r\n                transformContainer(translate3d, false);\r\n            }\r\n            else if(options.autoScrolling && !options.scrollBar){\r\n                container.css('top', -roundedTop);\r\n            }\r\n            else{\r\n                $htmlBody.scrollTop(roundedTop);\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Returns the cross-browser transform string.\r\n        */\r\n        function getTransforms(translate3d){\r\n            return {\r\n                '-webkit-transform': translate3d,\r\n                '-moz-transform': translate3d,\r\n                '-ms-transform':translate3d,\r\n                'transform': translate3d\r\n            };\r\n        }\r\n\r\n        /**\r\n        * Allowing or disallowing the mouse/swipe scroll in a given direction. (not for keyboard)\r\n        * @type  m (mouse) or k (keyboard)\r\n        */\r\n        function setIsScrollAllowed(value, direction, type){\r\n            //up, down, left, right\r\n            if(direction !== 'all'){\r\n                isScrollAllowed[type][direction] = value;\r\n            }\r\n\r\n            //all directions?\r\n            else{\r\n                $.each(Object.keys(isScrollAllowed[type]), function(index, key){\r\n                    isScrollAllowed[type][key] = value;\r\n                });\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Destroys fullpage.js plugin events and optinally its html markup and styles\r\n        */\r\n        function destroy(all){\r\n            setAutoScrolling(false, 'internal');\r\n            setAllowScrolling(false);\r\n            setKeyboardScrolling(false);\r\n            container.addClass(DESTROYED);\r\n\r\n            clearTimeout(afterSlideLoadsId);\r\n            clearTimeout(afterSectionLoadsId);\r\n            clearTimeout(resizeId);\r\n            clearTimeout(scrollId);\r\n            clearTimeout(scrollId2);\r\n\r\n            $window\r\n                .off('scroll', scrollHandler)\r\n                .off('hashchange', hashChangeHandler)\r\n                .off('resize', resizeHandler);\r\n\r\n            $document\r\n                .off('keydown', keydownHandler)\r\n                .off('keyup', keyUpHandler)\r\n                .off('click touchstart', SECTION_NAV_SEL + ' a')\r\n                .off('mouseenter', SECTION_NAV_SEL + ' li')\r\n                .off('mouseleave', SECTION_NAV_SEL + ' li')\r\n                .off('click touchstart', SLIDES_NAV_LINK_SEL)\r\n                .off('mouseover', options.normalScrollElements)\r\n                .off('mouseout', options.normalScrollElements);\r\n\r\n            $(SECTION_SEL)\r\n                .off('click touchstart', SLIDES_ARROW_SEL);\r\n\r\n            clearTimeout(afterSlideLoadsId);\r\n            clearTimeout(afterSectionLoadsId);\r\n\r\n            //lets make a mess!\r\n            if(all){\r\n                destroyStructure();\r\n            }\r\n        }\r\n\r\n        /*\r\n        * Removes inline styles added by fullpage.js\r\n        */\r\n        function destroyStructure(){\r\n            //reseting the `top` or `translate` properties to 0\r\n            silentScroll(0);\r\n\r\n            //loading all the lazy load content\r\n            container.find('img[data-src], source[data-src], audio[data-src], iframe[data-src]').each(function(){\r\n                setSrc($(this), 'src');\r\n            });\r\n\r\n            container.find('img[data-srcset]').each(function(){\r\n                setSrc($(this), 'srcset');\r\n            });\r\n\r\n            $(SECTION_NAV_SEL + ', ' + SLIDES_NAV_SEL +  ', ' + SLIDES_ARROW_SEL).remove();\r\n\r\n            //removing inline styles\r\n            $(SECTION_SEL).css( {\r\n                'height': '',\r\n                'background-color' : '',\r\n                'padding': ''\r\n            });\r\n\r\n            $(SLIDE_SEL).css( {\r\n                'width': ''\r\n            });\r\n\r\n            container.css({\r\n                'height': '',\r\n                'position': '',\r\n                '-ms-touch-action': '',\r\n                'touch-action': ''\r\n            });\r\n\r\n            $htmlBody.css({\r\n                'overflow': '',\r\n                'height': ''\r\n            });\r\n\r\n            // remove .fp-enabled class\r\n            $('html').removeClass(ENABLED);\r\n\r\n            // remove .fp-responsive class\r\n            $body.removeClass(RESPONSIVE);\r\n\r\n            // remove all of the .fp-viewing- classes\r\n            $.each($body.get(0).className.split(/\\s+/), function (index, className) {\r\n                if (className.indexOf(VIEWING_PREFIX) === 0) {\r\n                    $body.removeClass(className);\r\n                }\r\n            });\r\n\r\n            //removing added classes\r\n            $(SECTION_SEL + ', ' + SLIDE_SEL).each(function(){\r\n                if(options.scrollOverflowHandler){\r\n                    options.scrollOverflowHandler.remove($(this));\r\n                }\r\n                $(this).removeClass(TABLE + ' ' + ACTIVE);\r\n                $(this).attr('style', $(this).data('fp-styles'));\r\n            });\r\n\r\n            removeAnimation(container);\r\n\r\n            //Unwrapping content\r\n            container.find(TABLE_CELL_SEL + ', ' + SLIDES_CONTAINER_SEL + ', ' + SLIDES_WRAPPER_SEL).each(function(){\r\n                //unwrap not being use in case there's no child element inside and its just text\r\n                $(this).replaceWith(this.childNodes);\r\n            });\r\n\r\n            //removing the applied transition from the fullpage wrapper\r\n            container.css({\r\n                '-webkit-transition': 'none',\r\n                'transition': 'none'\r\n            });\r\n\r\n            //scrolling the page to the top with no animation\r\n            $htmlBody.scrollTop(0);\r\n\r\n            //removing selectors\r\n            var usedSelectors = [SECTION, SLIDE, SLIDES_CONTAINER];\r\n            $.each(usedSelectors, function(index, value){\r\n                $('.' + value).removeClass(value);\r\n            });\r\n        }\r\n\r\n        /*\r\n        * Sets the state for a variable with multiple states (original, and temporal)\r\n        * Some variables such as `autoScrolling` or `recordHistory` might change automatically its state when using `responsive` or `autoScrolling:false`.\r\n        * This function is used to keep track of both states, the original and the temporal one.\r\n        * If type is not 'internal', then we assume the user is globally changing the variable.\r\n        */\r\n        function setVariableState(variable, value, type){\r\n            options[variable] = value;\r\n            if(type !== 'internal'){\r\n                originals[variable] = value;\r\n            }\r\n        }\r\n\r\n        /**\r\n        * Displays warnings\r\n        */\r\n        function displayWarnings(){\r\n            var extensions = ['fadingEffect', 'continuousHorizontal', 'scrollHorizontally', 'interlockedSlides', 'resetSliders', 'responsiveSlides', 'offsetSections', 'dragAndMove', 'scrollOverflowReset', 'parallax'];\r\n            if($('html').hasClass(ENABLED)){\r\n                showError('error', 'Fullpage.js can only be initialized once and you are doing it multiple times!');\r\n                return;\r\n            }\r\n\r\n            // Disable mutually exclusive settings\r\n            if (options.continuousVertical &&\r\n                (options.loopTop || options.loopBottom)) {\r\n                options.continuousVertical = false;\r\n                showError('warn', 'Option `loopTop/loopBottom` is mutually exclusive with `continuousVertical`; `continuousVertical` disabled');\r\n            }\r\n\r\n            if(options.scrollBar && options.scrollOverflow){\r\n                showError('warn', 'Option `scrollBar` is mutually exclusive with `scrollOverflow`. Sections with scrollOverflow might not work well in Firefox');\r\n            }\r\n\r\n            if(options.continuousVertical && (options.scrollBar || !options.autoScrolling)){\r\n                options.continuousVertical = false;\r\n                showError('warn', 'Scroll bars (`scrollBar:true` or `autoScrolling:false`) are mutually exclusive with `continuousVertical`; `continuousVertical` disabled');\r\n            }\r\n\r\n            if(options.scrollOverflow && !options.scrollOverflowHandler){\r\n                options.scrollOverflow = false;\r\n                showError('error', 'The option `scrollOverflow:true` requires the file `scrolloverflow.min.js`. Please include it before fullPage.js.');\r\n            }\r\n\r\n            //using extensions? Wrong file!\r\n            $.each(extensions, function(index, extension){\r\n                //is the option set to true?\r\n                if(options[extension]){\r\n                    showError('warn', 'fullpage.js extensions require jquery.fullpage.extensions.min.js file instead of the usual jquery.fullpage.js. Requested: '+ extension);\r\n                }\r\n            });\r\n\r\n            //anchors can not have the same value as any element ID or NAME\r\n            $.each(options.anchors, function(index, name){\r\n\r\n                //case insensitive selectors (http://stackoverflow.com/a/19465187/1081396)\r\n                var nameAttr = $document.find('[name]').filter(function() {\r\n                    return $(this).attr('name') && $(this).attr('name').toLowerCase() == name.toLowerCase();\r\n                });\r\n\r\n                var idAttr = $document.find('[id]').filter(function() {\r\n                    return $(this).attr('id') && $(this).attr('id').toLowerCase() == name.toLowerCase();\r\n                });\r\n\r\n                if(idAttr.length || nameAttr.length ){\r\n                    showError('error', 'data-anchor tags can not have the same value as any `id` element on the site (or `name` element for IE).');\r\n                    idAttr.length && showError('error', '\"' + name + '\" is is being used by another element `id` property');\r\n                    nameAttr.length && showError('error', '\"' + name + '\" is is being used by another element `name` property');\r\n                }\r\n            });\r\n        }\r\n\r\n        /**\r\n        * Shows a message in the console of the given type.\r\n        */\r\n        function showError(type, text){\r\n            console && console[type] && console[type]('fullPage: ' + text);\r\n        }\r\n\r\n    }; //end of $.fn.fullpage\r\n});\r\n"]}