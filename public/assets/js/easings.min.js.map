{"version": 3, "sources": ["easings.js"], "names": ["window", "fp_easings", "def", "linear", "t", "b", "c", "d", "swing", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "Math", "cos", "PI", "easeOutSine", "sin", "easeInOutSine", "easeInExpo", "pow", "easeOutExpo", "easeInOutExpo", "easeInCirc", "sqrt", "easeOutCirc", "easeInOutCirc", "easeInElastic", "s", "p", "a", "abs", "asin", "easeOutElastic", "easeInOutElastic", "easeInBack", "undefined", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "easeInOutBounce"], "mappings": "AAoCAA,OAAOC,WAAa,CAChBC,IAAK,cACLC,OAAQ,SAAUC,EAAGC,EAAGC,EAAGC,GACvB,OAAOD,EAAEF,EAAEG,EAAIF,GAEnBG,MAAO,SAAUJ,EAAGC,EAAGC,EAAGC,GACtB,OAAOP,OAAOC,WAAWD,OAAOC,WAAWC,KAAKE,EAAGC,EAAGC,EAAGC,IAE7DE,WAAY,SAAUL,EAAGC,EAAGC,EAAGC,GAC3B,OAAOD,GAAGF,GAAGG,GAAGH,EAAIC,GAExBK,YAAa,SAAUN,EAAGC,EAAGC,EAAGC,GAC5B,OAAQD,GAAIF,GAAGG,IAAIH,EAAE,GAAKC,GAE9BM,cAAe,SAAUP,EAAGC,EAAGC,EAAGC,GAC9B,OAAKH,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAEF,EAAEA,EAAIC,GAC3BC,EAAE,KAAQF,GAAIA,EAAE,GAAK,GAAKC,GAEtCO,YAAa,SAAUR,EAAGC,EAAGC,EAAGC,GAC5B,OAAOD,GAAGF,GAAGG,GAAGH,EAAEA,EAAIC,GAE1BQ,aAAc,SAAUT,EAAGC,EAAGC,EAAGC,GAC7B,OAAOD,IAAIF,EAAEA,EAAEG,EAAE,GAAGH,EAAEA,EAAI,GAAKC,GAEnCS,eAAgB,SAAUV,EAAGC,EAAGC,EAAGC,GAC/B,OAAKH,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAEF,EAAEA,EAAEA,EAAIC,EAC9BC,EAAE,IAAIF,GAAG,GAAGA,EAAEA,EAAI,GAAKC,GAElCU,YAAa,SAAUX,EAAGC,EAAGC,EAAGC,GAC5B,OAAOD,GAAGF,GAAGG,GAAGH,EAAEA,EAAEA,EAAIC,GAE5BW,aAAc,SAAUZ,EAAGC,EAAGC,EAAGC,GAC7B,OAAQD,IAAMF,EAAEA,EAAEG,EAAE,GAAGH,EAAEA,EAAEA,EAAI,GAAKC,GAExCY,eAAgB,SAAUb,EAAGC,EAAGC,EAAGC,GAC/B,OAAKH,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAEF,EAAEA,EAAEA,EAAEA,EAAIC,GAC/BC,EAAE,IAAMF,GAAG,GAAGA,EAAEA,EAAEA,EAAI,GAAKC,GAEvCa,YAAa,SAAUd,EAAGC,EAAGC,EAAGC,GAC5B,OAAOD,GAAGF,GAAGG,GAAGH,EAAEA,EAAEA,EAAEA,EAAIC,GAE9Bc,aAAc,SAAUf,EAAGC,EAAGC,EAAGC,GAC7B,OAAOD,IAAIF,EAAEA,EAAEG,EAAE,GAAGH,EAAEA,EAAEA,EAAEA,EAAI,GAAKC,GAEvCe,eAAgB,SAAUhB,EAAGC,EAAGC,EAAGC,GAC/B,OAAKH,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAEF,EAAEA,EAAEA,EAAEA,EAAEA,EAAIC,EAClCC,EAAE,IAAIF,GAAG,GAAGA,EAAEA,EAAEA,EAAEA,EAAI,GAAKC,GAEtCgB,WAAY,SAAUjB,EAAGC,EAAGC,EAAGC,GAC3B,OAAQD,EAAIgB,KAAKC,IAAInB,EAAEG,GAAKe,KAAKE,GAAG,IAAMlB,EAAID,GAElDoB,YAAa,SAAUrB,EAAGC,EAAGC,EAAGC,GAC5B,OAAOD,EAAIgB,KAAKI,IAAItB,EAAEG,GAAKe,KAAKE,GAAG,IAAMnB,GAE7CsB,cAAe,SAAUvB,EAAGC,EAAGC,EAAGC,GAC9B,OAAQD,EAAE,GAAKgB,KAAKC,IAAID,KAAKE,GAAGpB,EAAEG,GAAK,GAAKF,GAEhDuB,WAAY,SAAUxB,EAAGC,EAAGC,EAAGC,GAC3B,OAAW,GAAHH,EAAQC,EAAIC,EAAIgB,KAAKO,IAAI,EAAG,IAAMzB,EAAEG,EAAI,IAAMF,GAE1DyB,YAAa,SAAU1B,EAAGC,EAAGC,EAAGC,GAC5B,OAAQH,GAAGG,EAAKF,EAAEC,EAAIA,GAA+B,EAAzBgB,KAAKO,IAAI,GAAI,GAAKzB,EAAEG,IAAUF,GAE9D0B,cAAe,SAAU3B,EAAGC,EAAGC,EAAGC,GAC9B,OAAO,GAAHH,EAAaC,EACbD,GAAGG,EAAUF,EAAEC,GACdF,GAAGG,EAAE,GAAK,EAAUD,EAAE,EAAIgB,KAAKO,IAAI,EAAG,IAAMzB,EAAI,IAAMC,EACpDC,EAAE,GAA+B,EAAzBgB,KAAKO,IAAI,GAAI,KAAOzB,IAAUC,GAEjD2B,WAAY,SAAU5B,EAAGC,EAAGC,EAAGC,GAC3B,OAAQD,GAAKgB,KAAKW,KAAK,GAAK7B,GAAGG,GAAGH,GAAK,GAAKC,GAEhD6B,YAAa,SAAU9B,EAAGC,EAAGC,EAAGC,GAC5B,OAAOD,EAAIgB,KAAKW,KAAK,GAAK7B,EAAEA,EAAEG,EAAE,GAAGH,GAAKC,GAE5C8B,cAAe,SAAU/B,EAAGC,EAAGC,EAAGC,GAC9B,OAAKH,GAAGG,EAAE,GAAK,GAAWD,EAAE,GAAKgB,KAAKW,KAAK,EAAI7B,EAAEA,GAAK,GAAKC,EACpDC,EAAE,GAAKgB,KAAKW,KAAK,GAAK7B,GAAG,GAAGA,GAAK,GAAKC,GAEjD+B,cAAe,SAAUhC,EAAGC,EAAGC,EAAGC,GAC9B,IAAI8B,EAAE,QAAYC,EAAE,EAAMC,EAAEjC,EAC5B,GAAO,GAAHF,EAAM,OAAOC,EAAI,GAAY,IAAPD,GAAGG,GAAO,OAAOF,EAAEC,EAC7C,GADsDgC,IAAGA,EAAI,GAAF/B,GACvDgC,EAAIjB,KAAKkB,IAAIlC,GAAI,CAAEiC,EAAEjC,EAAO+B,EAAEC,EAAE,OAC3BD,EAAIC,GAAG,EAAEhB,KAAKE,IAAMF,KAAKmB,KAAMnC,EAAEiC,GAC1C,OAASA,EAAEjB,KAAKO,IAAI,EAAE,IAAIzB,GAAG,IAAMkB,KAAKI,KAAMtB,EAAEG,EAAE8B,IAAI,EAAEf,KAAKE,IAAIc,GAAOjC,GAE5EqC,eAAgB,SAAUtC,EAAGC,EAAGC,EAAGC,GAC/B,IAAI8B,EAAE,QAAYC,EAAE,EAAMC,EAAEjC,EAC5B,GAAO,GAAHF,EAAM,OAAOC,EAAI,GAAY,IAAPD,GAAGG,GAAO,OAAOF,EAAEC,EAC7C,GADsDgC,IAAGA,EAAI,GAAF/B,GACvDgC,EAAIjB,KAAKkB,IAAIlC,GAAI,CAAEiC,EAAEjC,EAAO+B,EAAEC,EAAE,OAC3BD,EAAIC,GAAG,EAAEhB,KAAKE,IAAMF,KAAKmB,KAAMnC,EAAEiC,GAC1C,OAAOA,EAAEjB,KAAKO,IAAI,GAAG,GAAGzB,GAAKkB,KAAKI,KAAMtB,EAAEG,EAAE8B,IAAI,EAAEf,KAAKE,IAAIc,GAAMhC,EAAID,GAEzEsC,iBAAkB,SAAUvC,EAAGC,EAAGC,EAAGC,GACjC,IAAI8B,EAAE,QAAYC,EAAE,EAAMC,EAAEjC,EAC5B,GAAO,GAAHF,EAAM,OAAOC,EAAI,GAAc,IAATD,GAAGG,EAAE,GAAO,OAAOF,EAAEC,EAC/C,GADwDgC,IAAGA,EAAE/B,GAAG,GAAG,MAC/DgC,EAAIjB,KAAKkB,IAAIlC,GAAI,CAAEiC,EAAEjC,EAAO+B,EAAEC,EAAE,OAC3BD,EAAIC,GAAG,EAAEhB,KAAKE,IAAMF,KAAKmB,KAAMnC,EAAEiC,GAC1C,OAAInC,EAAI,EAAemC,EAAEjB,KAAKO,IAAI,EAAE,IAAIzB,GAAG,IAAMkB,KAAKI,KAAMtB,EAAEG,EAAE8B,IAAI,EAAEf,KAAKE,IAAIc,IAA5D,GAAmEjC,EAC/EkC,EAAEjB,KAAKO,IAAI,GAAG,IAAIzB,GAAG,IAAMkB,KAAKI,KAAMtB,EAAEG,EAAE8B,IAAI,EAAEf,KAAKE,IAAIc,GAAI,GAAKhC,EAAID,GAEjFuC,WAAY,SAAUxC,EAAGC,EAAGC,EAAGC,EAAG8B,GAE9B,OADSQ,MAALR,IAAgBA,EAAI,SACjB/B,GAAGF,GAAGG,GAAGH,IAAIiC,EAAE,GAAGjC,EAAIiC,GAAKhC,GAEtCyC,YAAa,SAAU1C,EAAGC,EAAGC,EAAGC,EAAG8B,GAE/B,OADSQ,MAALR,IAAgBA,EAAI,SACjB/B,IAAIF,EAAEA,EAAEG,EAAE,GAAGH,IAAIiC,EAAE,GAAGjC,EAAIiC,GAAK,GAAKhC,GAE/C0C,cAAe,SAAU3C,EAAGC,EAAGC,EAAGC,EAAG8B,GAEjC,OADSQ,MAALR,IAAgBA,EAAI,UACnBjC,GAAGG,EAAE,GAAK,EAAUD,EAAE,GAAGF,EAAEA,IAAiB,GAAZiC,GAAG,QAAYjC,EAAIiC,IAAMhC,EACvDC,EAAE,IAAIF,GAAG,GAAGA,IAAiB,GAAZiC,GAAG,QAAYjC,EAAIiC,GAAK,GAAKhC,GAEzD2C,aAAc,SAAU5C,EAAGC,EAAGC,EAAGC,GAC7B,OAAOD,EAAIN,OAAOC,WAAWgD,cAAe1C,EAAEH,EAAG,EAAGE,EAAGC,GAAKF,GAEhE4C,cAAe,SAAU7C,EAAGC,EAAGC,EAAGC,GAC9B,OAAKH,GAAGG,GAAM,EAAE,KACLD,GAAG,OAAOF,EAAEA,GAAKC,EACjBD,EAAK,EAAE,KACPE,GAAG,QAAQF,GAAI,IAAI,MAAOA,EAAI,KAAOC,EACrCD,EAAK,IAAI,KACTE,GAAG,QAAQF,GAAI,KAAK,MAAOA,EAAI,OAASC,EAExCC,GAAG,QAAQF,GAAI,MAAM,MAAOA,EAAI,SAAWC,GAG1D6C,gBAAiB,SAAU9C,EAAGC,EAAGC,EAAGC,GAChC,OAAIH,EAAIG,EAAE,EAA0D,GAAhDP,OAAOC,WAAW+C,aAAgB,EAAF5C,EAAK,EAAGE,EAAGC,GAAUF,EACf,GAAnDL,OAAOC,WAAWgD,cAAiB,EAAF7C,EAAIG,EAAG,EAAGD,EAAGC,GAAY,GAAFD,EAAOD", "file": "easings.min.js", "sourcesContent": ["/*!\n * jQuery Easing v1.3 - http://gsgd.co.uk/sandbox/jquery/easing/\n *\n * Uses the built in easing capabilities added In jQuery 1.1\n * to offer multiple easing options\n *\n * TERMS OF USE - jQuery Easing\n *\n * Open source under the BSD License.\n *\n * Copyright 2008 <PERSON><PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *\n * Redistributions of source code must retain the above copyright notice, this list of\n * conditions and the following disclaimer.\n * Redistributions in binary form must reproduce the above copyright notice, this list\n * of conditions and the following disclaimer in the documentation and/or other materials\n * provided with the distribution.\n *\n * Neither the name of the author nor the names of contributors may be used to endorse\n * or promote products derived from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY\n * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE\n * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE\n * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED\n * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED\n * OF THE POSSIBILITY OF SUCH DAMAGE.\n *\n*/\nwindow.fp_easings = {\n    def: 'easeOutQuad',\n    linear: function (t, b, c, d) {\n        return c*t/d + b\n    },\n    swing: function (t, b, c, d) {\n        return window.fp_easings[window.fp_easings.def](t, b, c, d);\n    },\n    easeInQuad: function (t, b, c, d) {\n        return c*(t/=d)*t + b;\n    },\n    easeOutQuad: function (t, b, c, d) {\n        return -c *(t/=d)*(t-2) + b;\n    },\n    easeInOutQuad: function (t, b, c, d) {\n        if ((t/=d/2) < 1) return c/2*t*t + b;\n        return -c/2 * ((--t)*(t-2) - 1) + b;\n    },\n    easeInCubic: function (t, b, c, d) {\n        return c*(t/=d)*t*t + b;\n    },\n    easeOutCubic: function (t, b, c, d) {\n        return c*((t=t/d-1)*t*t + 1) + b;\n    },\n    easeInOutCubic: function (t, b, c, d) {\n        if ((t/=d/2) < 1) return c/2*t*t*t + b;\n        return c/2*((t-=2)*t*t + 2) + b;\n    },\n    easeInQuart: function (t, b, c, d) {\n        return c*(t/=d)*t*t*t + b;\n    },\n    easeOutQuart: function (t, b, c, d) {\n        return -c * ((t=t/d-1)*t*t*t - 1) + b;\n    },\n    easeInOutQuart: function (t, b, c, d) {\n        if ((t/=d/2) < 1) return c/2*t*t*t*t + b;\n        return -c/2 * ((t-=2)*t*t*t - 2) + b;\n    },\n    easeInQuint: function (t, b, c, d) {\n        return c*(t/=d)*t*t*t*t + b;\n    },\n    easeOutQuint: function (t, b, c, d) {\n        return c*((t=t/d-1)*t*t*t*t + 1) + b;\n    },\n    easeInOutQuint: function (t, b, c, d) {\n        if ((t/=d/2) < 1) return c/2*t*t*t*t*t + b;\n        return c/2*((t-=2)*t*t*t*t + 2) + b;\n    },\n    easeInSine: function (t, b, c, d) {\n        return -c * Math.cos(t/d * (Math.PI/2)) + c + b;\n    },\n    easeOutSine: function (t, b, c, d) {\n        return c * Math.sin(t/d * (Math.PI/2)) + b;\n    },\n    easeInOutSine: function (t, b, c, d) {\n        return -c/2 * (Math.cos(Math.PI*t/d) - 1) + b;\n    },\n    easeInExpo: function (t, b, c, d) {\n        return (t==0) ? b : c * Math.pow(2, 10 * (t/d - 1)) + b;\n    },\n    easeOutExpo: function (t, b, c, d) {\n        return (t==d) ? b+c : c * (-Math.pow(2, -10 * t/d) + 1) + b;\n    },\n    easeInOutExpo: function (t, b, c, d) {\n        if (t==0) return b;\n        if (t==d) return b+c;\n        if ((t/=d/2) < 1) return c/2 * Math.pow(2, 10 * (t - 1)) + b;\n        return c/2 * (-Math.pow(2, -10 * --t) + 2) + b;\n    },\n    easeInCirc: function (t, b, c, d) {\n        return -c * (Math.sqrt(1 - (t/=d)*t) - 1) + b;\n    },\n    easeOutCirc: function (t, b, c, d) {\n        return c * Math.sqrt(1 - (t=t/d-1)*t) + b;\n    },\n    easeInOutCirc: function (t, b, c, d) {\n        if ((t/=d/2) < 1) return -c/2 * (Math.sqrt(1 - t*t) - 1) + b;\n        return c/2 * (Math.sqrt(1 - (t-=2)*t) + 1) + b;\n    },\n    easeInElastic: function (t, b, c, d) {\n        var s=1.70158;var p=0;var a=c;\n        if (t==0) return b;  if ((t/=d)==1) return b+c;  if (!p) p=d*.3;\n        if (a < Math.abs(c)) { a=c; var s=p/4; }\n        else var s = p/(2*Math.PI) * Math.asin (c/a);\n        return -(a*Math.pow(2,10*(t-=1)) * Math.sin( (t*d-s)*(2*Math.PI)/p )) + b;\n    },\n    easeOutElastic: function (t, b, c, d) {\n        var s=1.70158;var p=0;var a=c;\n        if (t==0) return b;  if ((t/=d)==1) return b+c;  if (!p) p=d*.3;\n        if (a < Math.abs(c)) { a=c; var s=p/4; }\n        else var s = p/(2*Math.PI) * Math.asin (c/a);\n        return a*Math.pow(2,-10*t) * Math.sin( (t*d-s)*(2*Math.PI)/p ) + c + b;\n    },\n    easeInOutElastic: function (t, b, c, d) {\n        var s=1.70158;var p=0;var a=c;\n        if (t==0) return b;  if ((t/=d/2)==2) return b+c;  if (!p) p=d*(.3*1.5);\n        if (a < Math.abs(c)) { a=c; var s=p/4; }\n        else var s = p/(2*Math.PI) * Math.asin (c/a);\n        if (t < 1) return -.5*(a*Math.pow(2,10*(t-=1)) * Math.sin( (t*d-s)*(2*Math.PI)/p )) + b;\n        return a*Math.pow(2,-10*(t-=1)) * Math.sin( (t*d-s)*(2*Math.PI)/p )*.5 + c + b;\n    },\n    easeInBack: function (t, b, c, d, s) {\n        if (s == undefined) s = 1.70158;\n        return c*(t/=d)*t*((s+1)*t - s) + b;\n    },\n    easeOutBack: function (t, b, c, d, s) {\n        if (s == undefined) s = 1.70158;\n        return c*((t=t/d-1)*t*((s+1)*t + s) + 1) + b;\n    },\n    easeInOutBack: function (t, b, c, d, s) {\n        if (s == undefined) s = 1.70158;\n        if ((t/=d/2) < 1) return c/2*(t*t*(((s*=(1.525))+1)*t - s)) + b;\n        return c/2*((t-=2)*t*(((s*=(1.525))+1)*t + s) + 2) + b;\n    },\n    easeInBounce: function (t, b, c, d) {\n        return c - window.fp_easings.easeOutBounce (d-t, 0, c, d) + b;\n    },\n    easeOutBounce: function (t, b, c, d) {\n        if ((t/=d) < (1/2.75)) {\n            return c*(7.5625*t*t) + b;\n        } else if (t < (2/2.75)) {\n            return c*(7.5625*(t-=(1.5/2.75))*t + .75) + b;\n        } else if (t < (2.5/2.75)) {\n            return c*(7.5625*(t-=(2.25/2.75))*t + .9375) + b;\n        } else {\n            return c*(7.5625*(t-=(2.625/2.75))*t + .984375) + b;\n        }\n    },\n    easeInOutBounce: function (t, b, c, d) {\n        if (t < d/2) return window.fp_easings.easeInBounce (t*2, 0, c, d) * .5 + b;\n        return window.fp_easings.easeOutBounce (t*2-d, 0, c, d) * .5 + c*.5 + b;\n    }\n};\n"]}